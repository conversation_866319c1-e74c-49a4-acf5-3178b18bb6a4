#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

const DIST_DIR = './dist';
const INDEX_HTML = path.join(DIST_DIR, 'index.html');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

console.log('🔍 Validating build output...\n');

// Check if dist directory exists
if (!fs.existsSync(DIST_DIR)) {
  console.error('❌ dist directory not found. Run `npm run build` first.');
  process.exit(1);
}

// Check if index.html exists
if (!fs.existsSync(INDEX_HTML)) {
  console.error('❌ index.html not found in dist directory.');
  process.exit(1);
}

// Read and validate index.html content
const indexContent = fs.readFileSync(INDEX_HTML, 'utf8');

// Check if it's a proper HTML file (not an export statement)
if (indexContent.startsWith('export default')) {
  console.error('❌ index.html contains export statement instead of HTML content.');
  console.error('   This indicates a Vite configuration issue.');
  console.error('   Make sure assetsInclude does not include HTML files.');
  process.exit(1);
}

// Check if it contains proper HTML structure
if (!indexContent.includes('<!doctype html>') || !indexContent.includes('<div id="root">')) {
  console.error('❌ index.html does not contain proper HTML structure.');
  process.exit(1);
}

// Check if assets directory exists
if (!fs.existsSync(ASSETS_DIR)) {
  console.error('❌ assets directory not found in dist.');
  process.exit(1);
}

// Check for CSS and JS files
const assetFiles = fs.readdirSync(ASSETS_DIR);
const cssFiles = assetFiles.filter(file => file.endsWith('.css'));
const jsFiles = assetFiles.filter(file => file.endsWith('.js'));

if (cssFiles.length === 0) {
  console.error('❌ No CSS files found in assets directory.');
  process.exit(1);
}

if (jsFiles.length === 0) {
  console.error('❌ No JS files found in assets directory.');
  process.exit(1);
}

// Check if index.html references the assets
const hasCssRef = cssFiles.some(file => indexContent.includes(file));
const hasJsRef = jsFiles.some(file => indexContent.includes(file));

if (!hasCssRef) {
  console.error('❌ index.html does not reference CSS assets.');
  process.exit(1);
}

if (!hasJsRef) {
  console.error('❌ index.html does not reference JS assets.');
  process.exit(1);
}

console.log('✅ Build validation passed!');
console.log(`   - index.html: ${(indexContent.length / 1024).toFixed(1)}KB`);
console.log(`   - CSS files: ${cssFiles.length} (${cssFiles.join(', ')})`);
console.log(`   - JS files: ${jsFiles.length} (${jsFiles.join(', ')})`);
console.log('\n🚀 Build is ready for deployment!');
