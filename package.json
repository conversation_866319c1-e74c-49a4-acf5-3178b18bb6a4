{"name": "youtube-subtitle-extractor", "private": true, "version": "1.0.0", "type": "module", "description": "Extract and download YouTube subtitles in multiple formats", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "health": "node health-check.js", "validate:vercel": "node validate-vercel.js", "types:supabase": "npx supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > src/types/supabase.ts", "clean": "rm -rf .next node_modules/.cache", "test:dev": "node scripts/test-health.js"}, "dependencies": {"@distube/ytdl-core": "^4.16.11", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^4.8.0", "@supabase/supabase-js": "^2.45.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.18.2", "lucide-react": "^0.515.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "socks-proxy-agent": "^8.0.5", "stripe": "^17.3.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "youtube-dl-exec": "^3.0.22", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "concurrently": "^9.1.2", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "postcss": "^8.4.38", "tailwindcss": "3.4.1", "typescript": "^5.8.2"}}