exports.id=312,exports.ids=[312],exports.modules={417:(e,t,s)=>{"use strict";s.d(t,{N:()=>l});var a=s(3939);let r=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",i=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key";if((!r||!i||r.includes("placeholder"))&&1)throw Error("Missing Supabase environment variables");let l=(0,a.createClient)(r,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},798:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d,O:()=>m});var r=s(8732),i=s(2015),l=s(417),n=s(2893),o=e([n]);n=(o.then?(await o)():o)[0];let c=(0,i.createContext)(void 0),d=()=>{let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},m=({children:e})=>{let[t,s]=(0,i.useState)(null),[a,o]=(0,i.useState)(!0),[d,m]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await l.N.auth.getSession();t?(console.error("Error getting session:",t),m(t.message)):s(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),m("Failed to get session")}finally{o(!1)}})();let{data:{subscription:e}}=l.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(s(t?.user||null),m(null),t?.user&&await u(t.user)):"SIGNED_OUT"===e&&(s(null),m(null)),o(!1)});return()=>e.unsubscribe()},[]);let u=async e=>{try{let{error:t}=await l.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},h=async()=>{try{o(!0),m(null);let{error:e}=await l.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(m(e.message),n.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),m("Failed to sign in"),n.default.error("Failed to sign in with Google")}finally{o(!1)}},x=async()=>{try{o(!0),m(null);let{error:e}=await l.N.auth.signOut();e?(m(e.message),n.default.error("Failed to sign out")):n.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),m("Failed to sign out"),n.default.error("Failed to sign out")}finally{o(!1)}},p=async()=>{try{let{data:{user:e},error:t}=await l.N.auth.getUser();t?m(t.message):s(e)}catch(e){console.error("Error refreshing user:",e),m("Failed to refresh user")}};return(0,r.jsx)(c.Provider,{value:{user:t,loading:a,error:d,signInWithGoogle:h,signOut:x,refreshUser:p},children:e})};a()}catch(e){a(e)}})},849:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{R:()=>d});var r=s(2015),i=s(417),l=s(2796),n=s(798),o=s(2893),c=e([n,o]);[n,o]=c.then?(await c)():c;let d=()=>{let{user:e}=(0,n.A)(),[t,s]=(0,r.useState)(null),[a,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(!0),[u,h]=(0,r.useState)(null);(0,r.useEffect)(()=>{e?x():(s(null),c(null),m(!1))},[e]);let x=async()=>{if(e)try{m(!0),h(null);let{data:t,error:a}=await i.N.from("subscriptions").select("*").eq("user_id",e.id).eq("status","active").single();if(a&&"PGRST116"!==a.code)throw a;let{data:r,error:l}=await i.N.from("usage_stats").select("*").eq("user_id",e.id).single();if(l&&"PGRST116"!==l.code)throw l;s(t),c(r)}catch(e){console.error("Error fetching subscription data:",e),h("Failed to fetch subscription data")}finally{m(!1)}},p=async t=>{if(!e)throw Error("User must be authenticated");try{let s=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:l.D[t].priceId,userId:e.id,userEmail:e.email})});if(!s.ok)throw Error("Failed to create checkout session");let{sessionId:a}=await s.json();return a}catch(e){throw console.error("Error creating checkout session:",e),o.default.error("Failed to create checkout session"),e}},f=async()=>{if(!t)throw Error("No active subscription found");try{if(!(await fetch("/api/user/subscription",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({subscriptionId:t.stripe_subscription_id})})).ok)throw Error("Failed to cancel subscription");o.default.success("Subscription cancelled successfully"),await x()}catch(e){throw console.error("Error cancelling subscription:",e),o.default.error("Failed to cancel subscription"),e}},g=async()=>{await x()};return{subscription:t,usage:a,loading:d,error:u,canExtractVideo:(e=0)=>{if(!t||"active"!==t.status)return!1;let s=l.D[t.tier];return!!s&&(-1===s.limits.maxVideoLength||!(e>s.limits.maxVideoLength))&&(-1===s.limits.videosPerMonth||(a?.videos_extracted_this_month||0)<s.limits.videosPerMonth)},getRemainingExtractions:()=>{if(!t||"active"!==t.status)return 0;let e=l.D[t.tier];if(!e)return 0;if(-1===e.limits.videosPerMonth)return -1;let s=a?.videos_extracted_this_month||0;return Math.max(0,e.limits.videosPerMonth-s)},createCheckoutSession:p,cancelSubscription:f,refreshSubscription:g}};a()}catch(e){a(e)}})},1518:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{BK:()=>d,eu:()=>c,q5:()=>m});var r=s(8732),i=s(2015),l=s(2549),n=s(3678),o=e([l,n]);[l,n]=o.then?(await o)():o;let c=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(l.Root,{ref:s,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));c.displayName=l.Root.displayName;let d=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(l.Image,{ref:s,className:(0,n.cn)("aspect-square h-full w-full",e),...t}));d.displayName=l.Image.displayName;let m=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(l.Fallback,{ref:s,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));m.displayName=l.Fallback.displayName,a()}catch(e){a(e)}})},2237:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{BT:()=>m,Wu:()=>u,ZB:()=>d,Zp:()=>o,aR:()=>c,wL:()=>h});var r=s(8732),i=s(2015),l=s(3678),n=e([l]);l=(n.then?(await n)():n)[0];let o=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));o.displayName="Card";let c=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t}));c.displayName="CardHeader";let d=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let m=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));m.displayName="CardDescription";let u=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let h=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t}));h.displayName="CardFooter",a()}catch(e){a(e)}})},2386:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>o});var r=s(8732),i=s(2893),l=s(798);s(2768);var n=e([i,l]);function o({Component:e,pageProps:t}){return(0,r.jsxs)(l.O,{children:[(0,r.jsx)(e,{...t}),(0,r.jsx)(i.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[i,l]=n.then?(await n)():n,a()}catch(e){a(e)}})},2768:()=>{},2796:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,t:()=>i});var a=s(206);let r=process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;if(!r)throw Error("Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable");let i=(0,a.loadStripe)(r),l={starter:{name:"Starter",price:9,priceId:process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID||"price_starter_monthly",features:["Extract subtitles from 50 videos/month","VTT and TXT format downloads","Auto-generated caption support","Basic language detection","Email support"],limits:{videosPerMonth:50,maxVideoLength:60},popular:!1},pro:{name:"Pro",price:19,priceId:process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID||"price_pro_monthly",features:["Extract subtitles from 200 videos/month","All format downloads (VTT, TXT, SRT)","Manual and auto-generated captions","Advanced language detection","Batch processing","Priority support"],limits:{videosPerMonth:200,maxVideoLength:180},popular:!0},premium:{name:"Premium",price:39,priceId:process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID||"price_premium_monthly",features:["Unlimited video extractions","All format downloads","Manual and auto-generated captions","Advanced language detection","Batch processing","API access","Custom integrations","Priority support"],limits:{videosPerMonth:-1,maxVideoLength:-1},popular:!1}}},3678:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{cn:()=>n});var r=s(802),i=s(5979),l=e([r,i]);function n(...e){return(0,i.twMerge)((0,r.clsx)(e))}[r,i]=l.then?(await l)():l,a()}catch(e){a(e)}})},3996:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>d});var r=s(8732);s(2015);var i=s(7459),l=s(798),n=s(1335),o=s(4961),c=e([i,l]);[i,l]=c.then?(await c)():c;let d=({variant:e="default",size:t="default",className:s="",children:a})=>{let{signInWithGoogle:c,loading:d}=(0,l.A)();return(0,r.jsxs)(i.$,{onClick:c,disabled:d,variant:e,size:t,className:s,children:[d?(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),a||"Sign in with Google"]})};a()}catch(e){a(e)}})},4275:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>f});var r=s(8732);s(2015);var i=s(7459),l=s(4474),n=s(1518),o=s(798),c=s(849),d=s(2312),m=s(3838),u=s(8903),h=s(9048),x=s(1576),p=e([i,l,n,o,c]);[i,l,n,o,c]=p.then?(await p)():p;let f=({onNavigate:e})=>{let{user:t,signOut:s}=(0,o.A)(),{subscription:a}=(0,c.R)();if(!t)return null;let p=t.user_metadata?.full_name||t.user_metadata?.name||t.email?.split("@")[0]||"User",f=t.user_metadata?.avatar_url||t.user_metadata?.picture;return(0,r.jsxs)(l.rI,{children:[(0,r.jsx)(l.ty,{asChild:!0,children:(0,r.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsxs)(n.eu,{className:"h-8 w-8",children:[(0,r.jsx)(n.BK,{src:f,alt:p}),(0,r.jsx)(n.q5,{className:"bg-purple-600 text-white",children:p.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]})})}),(0,r.jsxs)(l.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(l.lp,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:p}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.email}),a&&(0,r.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,r.jsx)(d.A,{className:"w-3 h-3 text-yellow-500"}),(0,r.jsxs)("span",{className:"text-xs font-medium text-yellow-600 capitalize",children:[a.tier," Plan"]})]})]})}),(0,r.jsx)(l.mB,{}),(0,r.jsxs)(l._2,{onClick:()=>e("dashboard"),children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsxs)(l._2,{onClick:()=>e("pricing"),children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Subscription"})]}),(0,r.jsxs)(l._2,{onClick:()=>e("settings"),children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsx)(l.mB,{}),(0,r.jsxs)(l._2,{onClick:s,children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Sign out"})]})]})]})};a()}catch(e){a(e)}})},4474:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{SQ:()=>u,_2:()=>h,lp:()=>x,mB:()=>p,rI:()=>d,ty:()=>m});var r=s(8732),i=s(2015),l=s(6307),n=s(824),o=s(3678),c=e([l,o]);[l,o]=c.then?(await c)():c;let d=l.Root,m=l.Trigger;l.Group,l.Portal,l.Sub,l.RadioGroup,i.forwardRef(({className:e,inset:t,children:s,...a},i)=>(0,r.jsxs)(l.SubTrigger,{ref:i,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...a,children:[s,(0,r.jsx)(n.ChevronRightIcon,{className:"ml-auto h-4 w-4"})]})).displayName=l.SubTrigger.displayName,i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(l.SubContent,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=l.SubContent.displayName;let u=i.forwardRef(({className:e,sideOffset:t=4,...s},a)=>(0,r.jsx)(l.Portal,{children:(0,r.jsx)(l.Content,{ref:a,sideOffset:t,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));u.displayName=l.Content.displayName;let h=i.forwardRef(({className:e,inset:t,...s},a)=>(0,r.jsx)(l.Item,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...s}));h.displayName=l.Item.displayName,i.forwardRef(({className:e,children:t,checked:s,...a},i)=>(0,r.jsxs)(l.CheckboxItem,{ref:i,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.ItemIndicator,{children:(0,r.jsx)(n.CheckIcon,{className:"h-4 w-4"})})}),t]})).displayName=l.CheckboxItem.displayName,i.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(l.RadioItem,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.ItemIndicator,{children:(0,r.jsx)(n.DotFilledIcon,{className:"h-4 w-4 fill-current"})})}),t]})).displayName=l.RadioItem.displayName;let x=i.forwardRef(({className:e,inset:t,...s},a)=>(0,r.jsx)(l.Label,{ref:a,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...s}));x.displayName=l.Label.displayName;let p=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(l.Separator,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));p.displayName=l.Separator.displayName,a()}catch(e){a(e)}})},5027:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>h});var r=s(8732),i=s(824),l=s(3220),n=s(2554),o=s(8509),c=s(6596),d=s(3441),m=s(703),u=e([l]);l=(u.then?(await u)():u)[0];let h=({onTermsClick:e,onPrivacyClick:t,onDisclaimerClick:s})=>{let a=new Date().getFullYear(),u={product:[{name:"Features",href:"#features"},{name:"How to Use",href:"#how-to-use"},{name:"Use Cases",href:"#use-cases"},{name:"FAQ",href:"#faq"}],legal:[{name:"Privacy Policy",onClick:t},{name:"Terms of Service",onClick:e},{name:"Disclaimer",onClick:s}],social:[{name:"GitHub",href:"#",icon:(0,r.jsx)(i.GitHubLogoIcon,{className:"w-5 h-5"})},{name:"Twitter",href:"#",icon:(0,r.jsx)(i.TwitterLogoIcon,{className:"w-5 h-5"})},{name:"Email",href:"mailto:<EMAIL>",icon:(0,r.jsx)(n.A,{className:"w-5 h-5"})}]};return(0,r.jsx)("footer",{className:"bg-slate-900 border-t border-slate-800",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8",children:[(0,r.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"col-span-1 sm:col-span-2",children:[(0,r.jsxs)("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4",children:["YouTube Subtitle",(0,r.jsxs)("span",{className:"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:[" ","Extractor"]})]}),(0,r.jsx)("p",{className:"text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base",children:"The most advanced YouTube subtitle extraction tool. Extract, clean, and download subtitles from videos and playlists with professional-grade accuracy."}),(0,r.jsx)("div",{className:"flex space-x-3 sm:space-x-4",children:u.social.map(e=>(0,r.jsx)(l.motion.a,{href:e.href,whileHover:{scale:1.1},whileTap:{scale:.95},className:"text-gray-400 hover:text-purple-400 transition-colors duration-200","aria-label":e.name,children:e.icon},e.name))})]}),(0,r.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[(0,r.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Product"}),(0,r.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:u.product.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]}),(0,r.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,r.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Legal"}),(0,r.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:u.legal.map(e=>(0,r.jsx)("li",{children:e.onClick?(0,r.jsx)("button",{onClick:e.onClick,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name}):(0,r.jsx)("a",{className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]})]}),(0,r.jsxs)(l.motion.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left",children:[(0,r.jsxs)("span",{children:["\xa9 ",a," DownloadYTSubtitles.com. Made with"]}),(0,r.jsx)(o.A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1"}),(0,r.jsx)("span",{children:"for the community."})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,r.jsx)("span",{children:"Privacy First"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,r.jsx)("span",{children:"Free During Beta"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,r.jsx)("span",{children:"Professional Quality"})]})]})]})]})})};a()}catch(e){a(e)}})},7459:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{$:()=>m});var r=s(8732),i=s(2015),l=s(9640),n=s(8938),o=s(3678),c=e([l,n,o]);[l,n,o]=c.then?(await c)():c;let d=(0,n.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),m=i.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...i},n)=>{let c=a?l.Slot:"button";return(0,r.jsx)(c,{className:(0,o.cn)(d({variant:t,size:s,className:e})),ref:n,...i})});m.displayName="Button",a()}catch(e){a(e)}})},8405:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{A:()=>f});var r=s(8732),i=s(3220),l=s(7459),n=s(3295),o=s(6569),c=s(3441),d=s(8903),m=s(128),u=s(798),h=s(3996),x=s(4275),p=e([i,l,u,h,x]);[i,l,u,h,x]=p.then?(await p)():p;let f=({currentView:e,onNavigate:t,onFeedback:s})=>{let{user:a}=(0,u.A)();return(0,r.jsx)(i.motion.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-14 sm:h-16",children:[(0,r.jsxs)(i.motion.div,{whileHover:{scale:1.05},className:"flex items-center space-x-2 sm:space-x-3 cursor-pointer",onClick:()=>t("landing"),children:[(0,r.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"w-4 h-4 sm:w-6 sm:h-6 text-white"})}),(0,r.jsxs)("div",{className:"hidden xs:block",children:[(0,r.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"DownloadYTSubtitles"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 hidden sm:block",children:"YouTube Subtitle Extractor"})]}),(0,r.jsx)("div",{className:"block xs:hidden",children:(0,r.jsx)("h1",{className:"text-sm font-bold text-white",children:"DYTS"})})]}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsxs)(l.$,{variant:"landing"===e?"default":"ghost",size:"sm",onClick:()=>t("landing"),className:"landing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Home"]}),(0,r.jsxs)(l.$,{variant:"extractor"===e?"default":"ghost",size:"sm",onClick:()=>t("extractor"),className:"extractor"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Extract"]}),(0,r.jsxs)(l.$,{variant:"faq"===e?"default":"ghost",size:"sm",onClick:()=>t("faq"),className:"faq"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"FAQ"]}),(0,r.jsxs)(l.$,{variant:"pricing"===e?"default":"ghost",size:"sm",onClick:()=>t("pricing"),className:"pricing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Pricing"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3",children:[(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:s,className:"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),(0,r.jsx)("span",{className:"hidden xs:inline",children:"Feedback"}),(0,r.jsx)("span",{className:"xs:hidden",children:"FB"})]}),a?(0,r.jsx)(x.A,{onNavigate:t}):(0,r.jsx)(h.A,{variant:"outline",size:"sm",className:"border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white"}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>t("extractor"),className:"text-gray-300 hover:text-white p-2",children:(0,r.jsx)(n.A,{className:"w-4 h-4 sm:w-5 sm:h-5"})})})]})]}),(0,r.jsx)("div",{className:"md:hidden pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 overflow-x-auto",children:[(0,r.jsx)(l.$,{variant:"landing"===e?"default":"ghost",size:"sm",onClick:()=>t("landing"),className:`text-xs px-3 py-2 ${"landing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Home"}),(0,r.jsx)(l.$,{variant:"extractor"===e?"default":"ghost",size:"sm",onClick:()=>t("extractor"),className:`text-xs px-3 py-2 ${"extractor"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Extract"}),(0,r.jsx)(l.$,{variant:"faq"===e?"default":"ghost",size:"sm",onClick:()=>t("faq"),className:`text-xs px-3 py-2 ${"faq"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"FAQ"}),(0,r.jsx)(l.$,{variant:"pricing"===e?"default":"ghost",size:"sm",onClick:()=>t("pricing"),className:`text-xs px-3 py-2 ${"pricing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Pricing"})]})})]})})};a()}catch(e){a(e)}})}};