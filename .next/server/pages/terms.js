"use strict";(()=>{var e={};e.id=769,e.ids=[220,769],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1680:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3220:e=>{e.exports=import("framer-motion")},3613:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>d});var i=a(8732),r=a(9788),o=a.n(r),n=a(4233),c=a(8405),l=a(5027),u=a(3697),p=e([c,l,u]);function d(){let e=(0,n.useRouter)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(o(),{children:[(0,i.jsx)("title",{children:"Terms of Service - DownloadYTSubtitles"}),(0,i.jsx)("meta",{name:"description",content:"Terms of Service for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,i.jsx)(c.A,{currentView:"terms",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,i.jsx)(u.A,{onBack:()=>{e.push("/")}}),(0,i.jsx)(l.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[c,l,u]=p.then?(await p)():p,s()}catch(e){s(e)}})},3697:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{A:()=>m});var i=a(8732),r=a(3220),o=a(2237),n=a(7459),c=a(703),l=a(6596),u=a(4498),p=a(1680),d=e([r,o,n]);[r,o,n]=d.then?(await d)():d;let m=({onBack:e})=>{let t=[{title:"Acceptance of Terms",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),content:["By accessing and using YouTube Subtitle Extractor, you accept and agree to be bound by the terms and provision of this agreement.","If you do not agree to abide by the above, please do not use this service."]},{title:"Service Description",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["YouTube Subtitle Extractor is a free tool that extracts publicly available subtitle data from YouTube videos.","The service processes subtitle files that are already publicly accessible through YouTube's platform.","We do not store, cache, or retain any video content, subtitle data, or personal information.","All processing happens in real-time and data is immediately discarded after processing."]},{title:"Acceptable Use",icon:(0,i.jsx)(u.A,{className:"w-5 h-5"}),content:["You may use this service only for lawful purposes and in accordance with these Terms.","You agree not to use the service for any commercial purposes without explicit permission.","You must respect YouTube's Terms of Service and applicable copyright laws.","Bulk extraction or automated usage that may impact service availability is prohibited.","You are responsible for ensuring your use complies with applicable laws in your jurisdiction."]},{title:"Intellectual Property",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["The subtitle content extracted belongs to the original content creators and YouTube.","We do not claim ownership of any extracted subtitle content.","Users are responsible for respecting copyright and intellectual property rights.","Fair use principles should guide your use of extracted subtitle content."]},{title:"Disclaimer of Warranties",icon:(0,i.jsx)(u.A,{className:"w-5 h-5"}),content:["This service is provided 'as is' without any representations or warranties.","We do not guarantee the accuracy, completeness, or quality of extracted subtitles.","We are not responsible for any errors in subtitle content or formatting.","Service availability is not guaranteed and may be interrupted for maintenance."]},{title:"Limitation of Liability",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["In no event shall YouTube Subtitle Extractor be liable for any indirect, incidental, special, consequential, or punitive damages.","Our total liability shall not exceed the amount you paid for the service (which is currently $0).","You acknowledge that you use this service at your own risk."]},{title:"Privacy and Data",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["We do not collect, store, or process personal data.","No cookies are used for tracking purposes.","All subtitle processing happens locally in your browser or temporarily on our servers.","We do not share any data with third parties."]},{title:"Changes to Terms",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),content:["We reserve the right to modify these terms at any time.","Changes will be effective immediately upon posting on this page.","Continued use of the service constitutes acceptance of modified terms."]},{title:"Contact Information",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),content:["For questions about these Terms of Service, please contact us through our support channels.","We will respond to legitimate inquiries within a reasonable timeframe."]}];return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)(n.$,{variant:"ghost",onClick:e,className:"text-white hover:text-purple-400",children:[(0,i.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,i.jsx)("div",{className:"text-right",children:(0,i.jsxs)("p",{className:"text-gray-400 text-sm",children:["Last updated: ",new Date().toLocaleDateString()]})})]}),(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center mb-12",children:[(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Terms of Service"}),(0,i.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Please read these terms carefully before using YouTube Subtitle Extractor"})]}),(0,i.jsx)("div",{className:"space-y-6",children:t.map((e,t)=>(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(t+2)},children:(0,i.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,i.jsx)(o.aR,{children:(0,i.jsxs)(o.ZB,{className:"text-white flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,i.jsx)("span",{children:e.title})]})}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:e.content.map((e,t)=>(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e},t))})})]})},t))}),(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,i.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,i.jsxs)(o.Wu,{className:"p-6",children:[(0,i.jsx)("p",{className:"text-gray-300 mb-4",children:"By using YouTube Subtitle Extractor, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service."}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["These terms are effective as of ",new Date().toLocaleDateString()," and will remain in effect except with respect to any changes in their provisions in the future."]})]})})})]})})};s()}catch(e){s(e)}})},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},4498:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4911:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{config:()=>b,default:()=>d,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>y,routeModule:()=>S,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>g});var i=a(3885),r=a(237),o=a(1413),n=a(9616),c=a.n(n),l=a(2386),u=a(3613),p=e([l,u]);[l,u]=p.then?(await p)():p;let d=(0,o.M)(u,"default"),m=(0,o.M)(u,"getStaticProps"),h=(0,o.M)(u,"getStaticPaths"),x=(0,o.M)(u,"getServerSideProps"),b=(0,o.M)(u,"config"),y=(0,o.M)(u,"reportWebVitals"),g=(0,o.M)(u,"unstable_getStaticProps"),v=(0,o.M)(u,"unstable_getStaticPaths"),f=(0,o.M)(u,"unstable_getStaticParams"),w=(0,o.M)(u,"unstable_getServerProps"),j=(0,o.M)(u,"unstable_getServerSideProps"),S=new i.PagesRouteModule({definition:{kind:r.A.PAGES,page:"/terms",pathname:"/terms",bundlePath:"",filename:""},components:{App:l.default,Document:c()},userland:u});s()}catch(e){s(e)}})},5979:e=>{e.exports=import("tailwind-merge")},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[616,441,975,312],()=>a(4911));module.exports=s})();