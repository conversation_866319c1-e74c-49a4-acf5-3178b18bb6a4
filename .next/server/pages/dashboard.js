(()=>{var e={};e.id=724,e.ids=[220,636,724],e.modules={5:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2015);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:i="",children:s,iconNode:u,...d},h)=>(0,a.createElement)("svg",{ref:h,...c,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",i),...!s&&!l(d)&&{"aria-hidden":"true"},...d},[...u.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(s)?s:[s]])),d=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},l)=>(0,a.createElement)(u,{ref:l,iconNode:t,className:o(`lucide-${n(s(e))}`,`lucide-${e}`,r),...i}));return r.displayName=s(e),r}},206:e=>{"use strict";e.exports=require("@stripe/stripe-js")},258:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(8732),i=r(9788),s=r.n(i),o=r(4233),l=r(6854),c=e([l]);function u(){let e=(0,o.useRouter)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(s(),{children:[(0,n.jsx)("title",{children:"Dashboard - DownloadYTSubtitles"}),(0,n.jsx)("meta",{name:"description",content:"User dashboard for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsx)(l.A,{onBack:()=>{e.push("/")},onNavigate:t=>{e.push(`/${t}`)}})]})}l=(c.then?(await c)():c)[0],a()}catch(e){a(e)}})},303:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Xi:()=>d,av:()=>h,j7:()=>u,tU:()=>c});var n=r(8732),i=r(2015),s=r(7259),o=r(3678),l=e([s,o]);[s,o]=l.then?(await l)():l;let c=s.Root,u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.List,{ref:r,className:(0,o.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...t}));u.displayName=s.List.displayName;let d=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.Trigger,{ref:r,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...t}));d.displayName=s.Trigger.displayName;let h=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.Content,{ref:r,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));h.displayName=s.Content.displayName,a()}catch(e){a(e)}})},361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},417:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var a=r(3939);let n=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",i=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key";if((!n||!i||n.includes("placeholder"))&&1)throw Error("Missing Supabase environment variables");let s=(0,a.createClient)(n,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},609:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||r&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},798:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>u,O:()=>d});var n=r(8732),i=r(2015),s=r(417),o=r(2893),l=e([o]);o=(l.then?(await l)():l)[0];let c=(0,i.createContext)(void 0),u=()=>{let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,i.useState)(null),[a,l]=(0,i.useState)(!0),[u,d]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await s.N.auth.getSession();t?(console.error("Error getting session:",t),d(t.message)):r(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),d("Failed to get session")}finally{l(!1)}})();let{data:{subscription:e}}=s.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(r(t?.user||null),d(null),t?.user&&await h(t.user)):"SIGNED_OUT"===e&&(r(null),d(null)),l(!1)});return()=>e.unsubscribe()},[]);let h=async e=>{try{let{error:t}=await s.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},m=async()=>{try{l(!0),d(null);let{error:e}=await s.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(d(e.message),o.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),d("Failed to sign in"),o.default.error("Failed to sign in with Google")}finally{l(!1)}},f=async()=>{try{l(!0),d(null);let{error:e}=await s.N.auth.signOut();e?(d(e.message),o.default.error("Failed to sign out")):o.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),d("Failed to sign out"),o.default.error("Failed to sign out")}finally{l(!1)}},g=async()=>{try{let{data:{user:e},error:t}=await s.N.auth.getUser();t?d(t.message):r(e)}catch(e){console.error("Error refreshing user:",e),d("Failed to refresh user")}};return(0,n.jsx)(c.Provider,{value:{user:t,loading:a,error:u,signInWithGoogle:m,signOut:f,refreshUser:g},children:e})};a()}catch(e){a(e)}})},802:e=>{"use strict";e.exports=import("clsx")},849:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{R:()=>u});var n=r(2015),i=r(417),s=r(2796),o=r(798),l=r(2893),c=e([o,l]);[o,l]=c.then?(await c)():c;let u=()=>{let{user:e}=(0,o.A)(),[t,r]=(0,n.useState)(null),[a,c]=(0,n.useState)(null),[u,d]=(0,n.useState)(!0),[h,m]=(0,n.useState)(null);(0,n.useEffect)(()=>{e?f():(r(null),c(null),d(!1))},[e]);let f=async()=>{if(e)try{d(!0),m(null);let{data:t,error:a}=await i.N.from("subscriptions").select("*").eq("user_id",e.id).eq("status","active").single();if(a&&"PGRST116"!==a.code)throw a;let{data:n,error:s}=await i.N.from("usage_stats").select("*").eq("user_id",e.id).single();if(s&&"PGRST116"!==s.code)throw s;r(t),c(n)}catch(e){console.error("Error fetching subscription data:",e),m("Failed to fetch subscription data")}finally{d(!1)}},g=async t=>{if(!e)throw Error("User must be authenticated");try{let r=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:s.D[t].priceId,userId:e.id,userEmail:e.email})});if(!r.ok)throw Error("Failed to create checkout session");let{sessionId:a}=await r.json();return a}catch(e){throw console.error("Error creating checkout session:",e),l.default.error("Failed to create checkout session"),e}},p=async()=>{if(!t)throw Error("No active subscription found");try{if(!(await fetch("/api/user/subscription",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({subscriptionId:t.stripe_subscription_id})})).ok)throw Error("Failed to cancel subscription");l.default.success("Subscription cancelled successfully"),await f()}catch(e){throw console.error("Error cancelling subscription:",e),l.default.error("Failed to cancel subscription"),e}},x=async()=>{await f()};return{subscription:t,usage:a,loading:u,error:h,canExtractVideo:(e=0)=>{if(!t||"active"!==t.status)return!1;let r=s.D[t.tier];return!!r&&(-1===r.limits.maxVideoLength||!(e>r.limits.maxVideoLength))&&(-1===r.limits.videosPerMonth||(a?.videos_extracted_this_month||0)<r.limits.videosPerMonth)},getRemainingExtractions:()=>{if(!t||"active"!==t.status)return 0;let e=s.D[t.tier];if(!e)return 0;if(-1===e.limits.videosPerMonth)return -1;let r=a?.videos_extracted_this_month||0;return Math.max(0,e.limits.videosPerMonth-r)},createCheckoutSession:g,cancelSubscription:p,refreshSubscription:x}};a()}catch(e){a(e)}})},1025:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},1145:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return d}});let a=r(7020),n=r(3147),i=r(8732),s=n._(r(2015)),o=a._(r(8160)),l=r(7043),c=r(1523),u=r(609);function d(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(1025);let m=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:r}=t;return e.reduce(h,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,a={};return n=>{let i=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?i=!1:t.add(n.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=n.props[t],r=a[t]||new Set;("name"!==t||!s)&&r.has(e)?i=!1:(r.add(e),a[t]=r)}}}return i}}()).reverse().map((e,t)=>{let a=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:a})})}let g=function(e){let{children:t}=e,r=(0,s.useContext)(l.AmpStateContext),a=(0,s.useContext)(c.HeadManagerContext);return(0,i.jsx)(o.default,{reduceComponentsToState:f,headManager:a,inAmpMode:(0,u.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1518:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{BK:()=>u,eu:()=>c,q5:()=>d});var n=r(8732),i=r(2015),s=r(2549),o=r(3678),l=e([s,o]);[s,o]=l.then?(await l)():l;let c=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.Root,{ref:r,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));c.displayName=s.Root.displayName;let u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.Image,{ref:r,className:(0,o.cn)("aspect-square h-full w-full",e),...t}));u.displayName=s.Image.displayName;let d=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.Fallback,{ref:r,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));d.displayName=s.Fallback.displayName,a()}catch(e){a(e)}})},1680:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2015:e=>{"use strict";e.exports=require("react")},2237:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{BT:()=>d,Wu:()=>h,ZB:()=>u,Zp:()=>l,aR:()=>c,wL:()=>m});var n=r(8732),i=r(2015),s=r(3678),o=e([s]);s=(o.then?(await o)():o)[0];let l=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));l.displayName="Card";let c=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));c.displayName="CardHeader";let u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("font-semibold leading-none tracking-tight",e),...t}));u.displayName="CardTitle";let d=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let h=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));h.displayName="CardContent";let m=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter",a()}catch(e){a(e)}})},2312:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},2326:e=>{"use strict";e.exports=require("react-dom")},2386:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l});var n=r(8732),i=r(2893),s=r(798);r(2768);var o=e([i,s]);function l({Component:e,pageProps:t}){return(0,n.jsxs)(s.O,{children:[(0,n.jsx)(e,{...t}),(0,n.jsx)(i.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[i,s]=o.then?(await o)():o,a()}catch(e){a(e)}})},2477:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>p,default:()=>h,getServerSideProps:()=>g,getStaticPaths:()=>f,getStaticProps:()=>m,reportWebVitals:()=>x,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>y});var n=r(3885),i=r(237),s=r(1413),o=r(9616),l=r.n(o),c=r(2386),u=r(258),d=e([c,u]);[c,u]=d.then?(await d)():d;let h=(0,s.M)(u,"default"),m=(0,s.M)(u,"getStaticProps"),f=(0,s.M)(u,"getStaticPaths"),g=(0,s.M)(u,"getServerSideProps"),p=(0,s.M)(u,"config"),x=(0,s.M)(u,"reportWebVitals"),y=(0,s.M)(u,"unstable_getStaticProps"),b=(0,s.M)(u,"unstable_getStaticPaths"),w=(0,s.M)(u,"unstable_getStaticParams"),v=(0,s.M)(u,"unstable_getServerProps"),N=(0,s.M)(u,"unstable_getServerSideProps"),j=new n.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/dashboard",pathname:"/dashboard",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:u});a()}catch(e){a(e)}})},2549:e=>{"use strict";e.exports=import("@radix-ui/react-avatar")},2768:()=>{},2796:(e,t,r)=>{"use strict";r.d(t,{D:()=>s,t:()=>i});var a=r(206);let n=process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;if(!n)throw Error("Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable");let i=(0,a.loadStripe)(n),s={starter:{name:"Starter",price:9,priceId:process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID||"price_starter_monthly",features:["Extract subtitles from 50 videos/month","VTT and TXT format downloads","Auto-generated caption support","Basic language detection","Email support"],limits:{videosPerMonth:50,maxVideoLength:60},popular:!1},pro:{name:"Pro",price:19,priceId:process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID||"price_pro_monthly",features:["Extract subtitles from 200 videos/month","All format downloads (VTT, TXT, SRT)","Manual and auto-generated captions","Advanced language detection","Batch processing","Priority support"],limits:{videosPerMonth:200,maxVideoLength:180},popular:!0},premium:{name:"Premium",price:39,priceId:process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID||"price_premium_monthly",features:["Unlimited video extractions","All format downloads","Manual and auto-generated captions","Advanced language detection","Batch processing","API access","Custom integrations","Priority support"],limits:{videosPerMonth:-1,maxVideoLength:-1},popular:!1}}},2893:e=>{"use strict";e.exports=import("react-hot-toast")},3106:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3220:e=>{"use strict";e.exports=import("framer-motion")},3295:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},3678:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{cn:()=>o});var n=r(802),i=r(5979),s=e([n,i]);function o(...e){return(0,i.twMerge)((0,n.clsx)(e))}[n,i]=s.then?(await s)():s,a()}catch(e){a(e)}})},3838:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},3873:e=>{"use strict";e.exports=require("path")},3939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},4075:e=>{"use strict";e.exports=require("zlib")},4498:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5341:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>p});var n=r(8732);r(2015);var i=r(2237),s=r(6786),o=r(6076),l=r(849),c=r(2796),u=r(4498),d=r(2312),h=r(5453),m=r(3106),f=r(7309),g=e([i,s,o,l]);[i,s,o,l]=g.then?(await g)():g;let p=()=>{let{subscription:e,usage:t,loading:r}=(0,l.R)();if(r)return(0,n.jsx)(i.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,n.jsx)(i.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,n.jsx)("div",{className:"h-4 bg-slate-700 rounded w-1/3"}),(0,n.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/2"}),(0,n.jsx)("div",{className:"h-4 bg-slate-700 rounded w-2/3"})]})})});if(!e)return(0,n.jsx)(i.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,n.jsxs)(i.aR,{children:[(0,n.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,n.jsx)(u.A,{className:"w-5 h-5 text-yellow-500"}),"No Active Subscription"]}),(0,n.jsx)(i.BT,{className:"text-gray-300",children:"Subscribe to a plan to start extracting YouTube subtitles"})]})});let a=c.D[e.tier],g=t?.videos_extracted_this_month||0,p=a?.limits.videosPerMonth||0;return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(i.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,n.jsx)(d.A,{className:"w-5 h-5 text-yellow-500"}),a?.name," Plan"]}),(0,n.jsxs)(i.BT,{className:"text-gray-300 flex items-center gap-2",children:[(0,n.jsx)(s.E,{className:(e=>{switch(e){case"active":return"bg-green-600";case"trialing":return"bg-blue-600";case"past_due":return"bg-yellow-600";case"canceled":return"bg-red-600";default:return"bg-gray-600"}})(e.status),children:(e=>{switch(e){case"active":return"Active";case"trialing":return"Trial";case"past_due":return"Past Due";case"canceled":return"Canceled";default:return e}})(e.status)}),(0,n.jsxs)("span",{children:["$",a?.price,"/month"]})]})]}),(0,n.jsx)(i.Wu,{className:"space-y-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400 mb-1",children:[(0,n.jsx)(h.A,{className:"w-4 h-4"}),"Current Period"]}),(0,n.jsxs)("div",{className:"text-white",children:[(0,f.GP)(new Date(e.current_period_start),"MMM d")," - ",(0,f.GP)(new Date(e.current_period_end),"MMM d, yyyy")]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400 mb-1",children:[(0,n.jsx)(m.A,{className:"w-4 h-4"}),"Next Billing"]}),(0,n.jsx)("div",{className:"text-white",children:e.cancel_at_period_end?"Cancels at period end":(0,f.GP)(new Date(e.current_period_end),"MMM d, yyyy")})]})]})})]}),(0,n.jsxs)(i.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{className:"text-white",children:"Usage This Month"}),(0,n.jsx)(i.BT,{className:"text-gray-300",children:"Track your subtitle extraction usage"})]}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-400",children:"Videos Extracted"}),(0,n.jsxs)("span",{className:"text-white font-medium",children:[g," ",-1===p?"":`/ ${p}`]})]}),-1!==p&&(0,n.jsx)(o.k,{value:-1===p?0:g/p*100,className:"h-2"}),-1===p&&(0,n.jsx)("div",{className:"text-sm text-green-400 font-medium",children:"✨ Unlimited extractions"})]}),a&&(0,n.jsxs)("div",{className:"pt-4 border-t border-slate-700",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Plan Features"}),(0,n.jsx)("ul",{className:"space-y-1 text-sm text-gray-400",children:a.features.slice(0,3).map((e,t)=>(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),e]},t))})]})]})]})]})};a()}catch(e){a(e)}})},5453:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},5979:e=>{"use strict";e.exports=import("tailwind-merge")},6076:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{k:()=>c});var n=r(8732),i=r(2015),s=r(7947),o=r(3678),l=e([s,o]);[s,o]=l.then?(await l)():l;let c=i.forwardRef(({className:e,value:t,...r},a)=>(0,n.jsx)(s.Root,{ref:a,className:(0,o.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...r,children:(0,n.jsx)(s.Indicator,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));c.displayName=s.Root.displayName,a()}catch(e){a(e)}})},6786:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{E:()=>l});var n=r(8732);r(2015);var i=r(8938),s=r(3678),o=e([i,s]);[i,s]=o.then?(await o)():o;let c=(0,i.cva)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,s.cn)(c({variant:t}),e),...r})}a()}catch(e){a(e)}})},6854:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>v});var n=r(8732),i=r(2015),s=r.n(i),o=r(3220),l=r(7459),c=r(2237),u=r(303),d=r(798),h=r(849),m=r(5341),f=r(1680),g=r(3838),p=r(8903),x=r(9048),y=r(3295),b=r(1518),w=e([o,l,c,u,d,h,m,b]);[o,l,c,u,d,h,m,b]=w.then?(await w)():w;let v=({onBack:e,onNavigate:t})=>{let{user:r}=(0,d.A)(),{subscription:a,cancelSubscription:i}=(0,h.R)(),[w,v]=s().useState(!1);if(!r)return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:(0,n.jsx)(c.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 p-8",children:(0,n.jsxs)(c.Wu,{className:"text-center",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Access Denied"}),(0,n.jsx)("p",{className:"text-gray-300 mb-4",children:"Please sign in to access your dashboard."}),(0,n.jsx)(l.$,{onClick:e,variant:"outline",children:"Go Back"})]})})});let N=r.user_metadata?.full_name||r.user_metadata?.name||r.email?.split("@")[0]||"User",j=r.user_metadata?.avatar_url||r.user_metadata?.picture,k=async()=>{if(a&&window.confirm("Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period."))try{v(!0),await i()}catch(e){console.error("Error cancelling subscription:",e)}finally{v(!1)}};return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto px-3 sm:px-6 lg:px-8 py-12",children:[e&&(0,n.jsx)(o.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"mb-8",children:(0,n.jsxs)(l.$,{onClick:e,variant:"ghost",className:"text-gray-300 hover:text-white",children:[(0,n.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,n.jsx)(o.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"mb-8",children:(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,n.jsxs)(b.eu,{className:"h-16 w-16",children:[(0,n.jsx)(b.BK,{src:j,alt:N}),(0,n.jsx)(b.q5,{className:"bg-purple-600 text-white text-lg",children:N.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-white",children:N}),(0,n.jsx)("p",{className:"text-gray-300",children:r.email})]})]})}),(0,n.jsx)(o.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,n.jsxs)(u.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,n.jsxs)(u.j7,{className:"grid w-full grid-cols-3 bg-slate-800/80 backdrop-blur-sm border border-slate-700",children:[(0,n.jsxs)(u.Xi,{value:"overview",className:"data-[state=active]:bg-purple-600",children:[(0,n.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Overview"]}),(0,n.jsxs)(u.Xi,{value:"subscription",className:"data-[state=active]:bg-purple-600",children:[(0,n.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Subscription"]}),(0,n.jsxs)(u.Xi,{value:"settings",className:"data-[state=active]:bg-purple-600",children:[(0,n.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Settings"]})]}),(0,n.jsx)(u.av,{value:"overview",className:"space-y-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsx)(m.A,{}),(0,n.jsxs)(c.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsxs)(c.ZB,{className:"text-white flex items-center gap-2",children:[(0,n.jsx)(y.A,{className:"w-5 h-5"}),"Quick Actions"]}),(0,n.jsx)(c.BT,{className:"text-gray-300",children:"Common tasks and shortcuts"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-3",children:[(0,n.jsx)(l.$,{onClick:()=>t?.("extractor"),className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Extract Subtitles"}),(0,n.jsx)(l.$,{onClick:()=>t?.("pricing"),variant:"outline",className:"w-full border-slate-600 text-gray-300 hover:text-white",children:"View Plans"}),(0,n.jsx)(l.$,{onClick:()=>t?.("faq"),variant:"ghost",className:"w-full text-gray-300 hover:text-white",children:"Help & FAQ"})]})]})]})}),(0,n.jsx)(u.av,{value:"subscription",className:"space-y-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsx)(m.A,{})}),(0,n.jsxs)(c.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsx)(c.ZB,{className:"text-white",children:"Manage Subscription"}),(0,n.jsx)(c.BT,{className:"text-gray-300",children:"Update your plan or billing information"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-3",children:[(0,n.jsx)(l.$,{onClick:()=>t?.("pricing"),className:"w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700",children:"Change Plan"}),a&&"active"===a.status&&!a.cancel_at_period_end&&(0,n.jsx)(l.$,{onClick:k,disabled:w,variant:"destructive",className:"w-full",children:w?"Cancelling...":"Cancel Subscription"}),a?.cancel_at_period_end&&(0,n.jsx)("div",{className:"text-sm text-yellow-400 p-3 bg-yellow-400/10 rounded-lg border border-yellow-400/20",children:"Your subscription will cancel at the end of the current billing period."})]})]})]})}),(0,n.jsx)(u.av,{value:"settings",className:"space-y-6",children:(0,n.jsxs)(c.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsx)(c.ZB,{className:"text-white",children:"Account Settings"}),(0,n.jsx)(c.BT,{className:"text-gray-300",children:"Manage your account preferences"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Email"}),(0,n.jsx)("div",{className:"text-white bg-slate-700 p-3 rounded-lg",children:r.email})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Full Name"}),(0,n.jsx)("div",{className:"text-white bg-slate-700 p-3 rounded-lg",children:N})]}),(0,n.jsx)("div",{className:"pt-4 border-t border-slate-700",children:(0,n.jsx)("p",{className:"text-sm text-gray-400",children:"Account settings are managed through your Google account. Changes made there will be reflected here automatically."})})]})]})})]})})]})})};a()}catch(e){a(e)}})},7043:(e,t,r)=>{"use strict";e.exports=r(3885).vendored.contexts.AmpContext},7259:e=>{"use strict";e.exports=import("@radix-ui/react-tabs")},7309:(e,t,r)=>{"use strict";r.d(t,{GP:()=>B});let a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function n(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let i={date:n({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:n({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:n({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},s={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function o(e){return(t,r)=>{let a;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,n=r?.width?String(r.width):t;a=e.formattingValues[n]||e.formattingValues[t]}else{let t=e.defaultWidth,n=r?.width?String(r.width):e.defaultWidth;a=e.values[n]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function l(e){return(t,r={})=>{let a,n=r.width,i=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],s=t.match(i);if(!s)return null;let o=s[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(o)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(o));return a=e.valueCallback?e.valueCallback(c):c,{value:a=r.valueCallback?r.valueCallback(a):a,rest:t.slice(o.length)}}}let c={code:"en-US",formatDistance:(e,t,r)=>{let n,i=a[e];if(n="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),r?.addSuffix)if(r.comparison&&r.comparison>0)return"in "+n;else return n+" ago";return n},formatLong:i,formatRelative:(e,t,r,a)=>s[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),a=r%100;if(a>20||a<10)switch(a%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:o({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:o({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:o({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:o({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:o({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let a=t.match(e.matchPattern);if(!a)return null;let n=a[0],i=t.match(e.parsePattern);if(!i)return null;let s=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:s=r.valueCallback?r.valueCallback(s):s,rest:t.slice(n.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:l({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:l({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:l({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:l({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:l({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},u={};function d(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}function h(e){let t=d(e);return t.setHours(0,0,0,0),t}function m(e){let t=d(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function f(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function g(e,t){let r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??u.weekStartsOn??u.locale?.options?.weekStartsOn??0,a=d(e),n=a.getDay();return a.setDate(a.getDate()-(7*(n<r)+n-r)),a.setHours(0,0,0,0),a}function p(e){return g(e,{weekStartsOn:1})}function x(e){let t=d(e),r=t.getFullYear(),a=f(e,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);let n=p(a),i=f(e,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);let s=p(i);return t.getTime()>=n.getTime()?r+1:t.getTime()>=s.getTime()?r:r-1}function y(e,t){let r=d(e),a=r.getFullYear(),n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??u.firstWeekContainsDate??u.locale?.options?.firstWeekContainsDate??1,i=f(e,0);i.setFullYear(a+1,0,n),i.setHours(0,0,0,0);let s=g(i,t),o=f(e,0);o.setFullYear(a,0,n),o.setHours(0,0,0,0);let l=g(o,t);return r.getTime()>=s.getTime()?a+1:r.getTime()>=l.getTime()?a:a-1}function b(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let w={y(e,t){let r=e.getFullYear(),a=r>0?r:1-r;return b("yy"===t?a%100:a,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):b(r+1,2)},d:(e,t)=>b(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>b(e.getHours()%12||12,t.length),H:(e,t)=>b(e.getHours(),t.length),m:(e,t)=>b(e.getMinutes(),t.length),s:(e,t)=>b(e.getSeconds(),t.length),S(e,t){let r=t.length;return b(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},v={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},N={G:function(e,t,r){let a=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(a,{width:"abbreviated"});case"GGGGG":return r.era(a,{width:"narrow"});default:return r.era(a,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return w.y(e,t)},Y:function(e,t,r,a){let n=y(e,a),i=n>0?n:1-n;return"YY"===t?b(i%100,2):"Yo"===t?r.ordinalNumber(i,{unit:"year"}):b(i,t.length)},R:function(e,t){return b(x(e),t.length)},u:function(e,t){return b(e.getFullYear(),t.length)},Q:function(e,t,r){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return b(a,2);case"Qo":return r.ordinalNumber(a,{unit:"quarter"});case"QQQ":return r.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(a,{width:"narrow",context:"formatting"});default:return r.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,r){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return b(a,2);case"qo":return r.ordinalNumber(a,{unit:"quarter"});case"qqq":return r.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(a,{width:"narrow",context:"standalone"});default:return r.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,r){let a=e.getMonth();switch(t){case"M":case"MM":return w.M(e,t);case"Mo":return r.ordinalNumber(a+1,{unit:"month"});case"MMM":return r.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(a,{width:"narrow",context:"formatting"});default:return r.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,r){let a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return b(a+1,2);case"Lo":return r.ordinalNumber(a+1,{unit:"month"});case"LLL":return r.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(a,{width:"narrow",context:"standalone"});default:return r.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,r,a){let n=function(e,t){let r=d(e);return Math.round((g(r,t)-function(e,t){let r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??u.firstWeekContainsDate??u.locale?.options?.firstWeekContainsDate??1,a=y(e,t),n=f(e,0);return n.setFullYear(a,0,r),n.setHours(0,0,0,0),g(n,t)}(r,t))/6048e5)+1}(e,a);return"wo"===t?r.ordinalNumber(n,{unit:"week"}):b(n,t.length)},I:function(e,t,r){let a=function(e){let t=d(e);return Math.round((p(t)-function(e){let t=x(e),r=f(e,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),p(r)}(t))/6048e5)+1}(e);return"Io"===t?r.ordinalNumber(a,{unit:"week"}):b(a,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):w.d(e,t)},D:function(e,t,r){let a=function(e){let t=d(e);return function(e,t){let r=h(e),a=h(t);return Math.round((r-m(r)-(a-m(a)))/864e5)}(t,function(e){let t=d(e),r=f(e,0);return r.setFullYear(t.getFullYear(),0,1),r.setHours(0,0,0,0),r}(t))+1}(e);return"Do"===t?r.ordinalNumber(a,{unit:"dayOfYear"}):b(a,t.length)},E:function(e,t,r){let a=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,r,a){let n=e.getDay(),i=(n-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return b(i,2);case"eo":return r.ordinalNumber(i,{unit:"day"});case"eee":return r.day(n,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(n,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},c:function(e,t,r,a){let n=e.getDay(),i=(n-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return b(i,t.length);case"co":return r.ordinalNumber(i,{unit:"day"});case"ccc":return r.day(n,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(n,{width:"narrow",context:"standalone"});case"cccccc":return r.day(n,{width:"short",context:"standalone"});default:return r.day(n,{width:"wide",context:"standalone"})}},i:function(e,t,r){let a=e.getDay(),n=0===a?7:a;switch(t){case"i":return String(n);case"ii":return b(n,t.length);case"io":return r.ordinalNumber(n,{unit:"day"});case"iii":return r.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,r){let a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,r){let a,n=e.getHours();switch(a=12===n?v.noon:0===n?v.midnight:n/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,r){let a,n=e.getHours();switch(a=n>=17?v.evening:n>=12?v.afternoon:n>=4?v.morning:v.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return w.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):w.H(e,t)},K:function(e,t,r){let a=e.getHours()%12;return"Ko"===t?r.ordinalNumber(a,{unit:"hour"}):b(a,t.length)},k:function(e,t,r){let a=e.getHours();return(0===a&&(a=24),"ko"===t)?r.ordinalNumber(a,{unit:"hour"}):b(a,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):w.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):w.s(e,t)},S:function(e,t){return w.S(e,t)},X:function(e,t,r){let a=e.getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return k(a);case"XXXX":case"XX":return M(a);default:return M(a,":")}},x:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"x":return k(a);case"xxxx":case"xx":return M(a);default:return M(a,":")}},O:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+j(a,":");default:return"GMT"+M(a,":")}},z:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+j(a,":");default:return"GMT"+M(a,":")}},t:function(e,t,r){return b(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,r){return b(e.getTime(),t.length)}};function j(e,t=""){let r=e>0?"-":"+",a=Math.abs(e),n=Math.trunc(a/60),i=a%60;return 0===i?r+String(n):r+String(n)+t+b(i,2)}function k(e,t){return e%60==0?(e>0?"-":"+")+b(Math.abs(e)/60,2):M(e,t)}function M(e,t=""){let r=Math.abs(e);return(e>0?"-":"+")+b(Math.trunc(r/60),2)+t+b(r%60,2)}let P=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},S=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},_={p:S,P:(e,t)=>{let r,a=e.match(/(P+)(p+)?/)||[],n=a[1],i=a[2];if(!i)return P(e,t);switch(n){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",P(n,t)).replace("{{time}}",S(i,t))}},T=/^D+$/,E=/^Y+$/,A=["D","DD","YY","YYYY"],C=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,D=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,W=/''/g,R=/[a-zA-Z]/;function B(e,t,r){let a=r?.locale??u.locale??c,n=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??u.firstWeekContainsDate??u.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??u.weekStartsOn??u.locale?.options?.weekStartsOn??0,s=d(e);if(!((s instanceof Date||"object"==typeof s&&"[object Date]"===Object.prototype.toString.call(s)||"number"==typeof s)&&!isNaN(Number(d(s)))))throw RangeError("Invalid time value");let o=t.match(D).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,_[t])(e,a.formatLong):e}).join("").match(C).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(O);return t?t[1].replace(W,"'"):e}(e)};if(N[t])return{isToken:!0,value:e};if(t.match(R))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});a.localize.preprocessor&&(o=a.localize.preprocessor(s,o));let l={firstWeekContainsDate:n,weekStartsOn:i,locale:a};return o.map(n=>{if(!n.isToken)return n.value;let i=n.value;return(!r?.useAdditionalWeekYearTokens&&E.test(i)||!r?.useAdditionalDayOfYearTokens&&T.test(i))&&function(e,t,r){let a=function(e,t,r){let a="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(a),A.includes(e))throw RangeError(a)}(i,t,String(e)),(0,N[i[0]])(s,i,a.localize,l)}).join("")}},7459:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{$:()=>d});var n=r(8732),i=r(2015),s=r(9640),o=r(8938),l=r(3678),c=e([s,o,l]);[s,o,l]=c.then?(await c)():c;let u=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},o)=>{let c=a?s.Slot:"button";return(0,n.jsx)(c,{className:(0,l.cn)(u({variant:t,size:r,className:e})),ref:o,...i})});d.displayName="Button",a()}catch(e){a(e)}})},7910:e=>{"use strict";e.exports=require("stream")},7947:e=>{"use strict";e.exports=import("@radix-ui/react-progress")},8160:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let a=r(2015),n=()=>{},i=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function o(){if(r&&r.mountedInstances){let t=a.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8903:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},8938:e=>{"use strict";e.exports=import("class-variance-authority")},9021:e=>{"use strict";e.exports=require("fs")},9048:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(5).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9640:e=>{"use strict";e.exports=import("@radix-ui/react-slot")},9788:(e,t,r)=>{e.exports=r(1145)}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[616,441],()=>r(2477));module.exports=a})();