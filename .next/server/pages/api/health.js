"use strict";(()=>{var e={};e.id=189,e.ids=[189],e.modules={2794:(e,t,n)=>{n.r(t),n.d(t,{config:()=>d,default:()=>u,routeModule:()=>l});var r={};n.r(r),n.d(r,{default:()=>s});var o=n(3480),i=n(8667),a=n(6435);let s=(e,t)=>(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)?void t.status(200).end():"GET"!==e.method?void t.status(405).json({error:"Method not allowed"}):void t.status(200).json({status:"OK",message:"YouTube Subtitle Extractor API is running",timestamp:new Date().toISOString()}),u=(0,a.M)(r,"default"),d=(0,a.M)(r,"config"),l=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/health",pathname:"/api/health",bundlePath:"",filename:""},userland:r})},3480:(e,t,n)=>{e.exports=n(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var n=t(t.s=2794);module.exports=n})();