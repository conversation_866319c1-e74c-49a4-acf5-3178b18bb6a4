"use strict";(()=>{var e={};e.id=958,e.ids=[958],e.modules={358:e=>{e.exports=require("@distube/ytdl-core")},2497:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>g,routeModule:()=>h});var o={};r.r(o),r.d(o,{default:()=>f});var s=r(3480),n=r(8667),a=r(6435),i=r(358);let l=i.createProxyAgent(process.env.SOCKET_URL),u=async e=>{try{let t=await fetch(e),r=await t.text();if(r.includes("WEBVTT"))return d(r);try{let e=JSON.parse(r);if(e.events)return c(e)}catch(e){}return d(r)}catch(e){return console.error("Error downloading/parsing subtitles:",e),[]}},d=e=>{let t=[],r=e.split("\n");for(let e=0;e<r.length;e++){let o=r[e].trim();if(o.includes("--\x3e")){let[s,n]=o.split("--\x3e").map(e=>e.trim()),a=p(s),i=p(n),l=[];for(e++;e<r.length&&""!==r[e].trim()&&!r[e].includes("--\x3e");){let t=r[e].trim();if(t){let e=t.replace(/<[^>]*>/g,"").trim();e&&l.push(e)}e++}e--,l.length>0&&t.push({start:a,end:i,text:l.join(" ")})}}return t},c=e=>{let t=[];if(e.events){for(let r of e.events)if(r.segs){let e="";for(let t of r.segs)t.utf8&&(e+=t.utf8);e.trim()&&t.push({start:r.tStartMs/1e3,end:(r.tStartMs+r.dDurationMs)/1e3,text:e.trim()})}}return t},p=e=>{let t=e.split(":");if(3===t.length){let e=parseInt(t[0]);return 3600*e+60*parseInt(t[1])+parseFloat(t[2])}return 0},f=async(e,t)=>{if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return void t.status(200).end();if("GET"!==e.method)return void t.status(405).json({error:"Method not allowed"});try{let{params:r}=e.query;console.log("Request URL:",e.url),console.log("Query params:",e.query),console.log("Params:",r);let o=r.split("-")||[];if(o.length<2)return t.status(400).json({error:"Invalid parameters. Expected format: videoId-langCode"});let[s,n]=o;if(!s)return t.status(400).json({error:"Video ID is required"});if(!n)return t.status(400).json({error:"Language code is required"});console.log(`Processing request for video: ${s}, language: ${n}`);let a=await i.getInfo(`https://www.youtube.com/watch?v=${s}`,{agent:l}),d=[],c=null;if(!a.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks)return t.status(404).json({error:"No subtitles available for this video"});{let e=a.player_response.captions.playerCaptionsTracklistRenderer.captionTracks.find(e=>e.languageCode===n);if(!e)return t.status(404).json({error:"Subtitles not found for the specified language"});c=e.baseUrl,console.log(`Found subtitles for ${n}`)}c&&(d=await u(c)),t.status(200).json({videoId:s,title:a.videoDetails?.title||"YouTube Video",thumbnail:a.videoDetails?.thumbnails?.[0]?.url||`https://img.youtube.com/vi/${s}/hqdefault.jpg`,language:n,subtitles:d})}catch(e){console.error("Error downloading subtitles:",e),t.status(500).json({error:"Failed to download subtitles",details:e instanceof Error?e.message:"Unknown error"})}},g=(0,a.M)(o,"default"),m=(0,a.M)(o,"config"),h=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/subtitles/download/[...params]",pathname:"/api/subtitles/download/[...params]",bundlePath:"",filename:""},userland:o})},3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=2497);module.exports=r})();