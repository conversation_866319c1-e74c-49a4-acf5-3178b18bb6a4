"use strict";(()=>{var e={};e.id=431,e.ids=[431],e.modules={358:e=>{e.exports=require("@distube/ytdl-core")},2750:(e,t,n)=>{n.r(t),n.d(t,{config:()=>p,default:()=>c,routeModule:()=>h});var r={};n.r(r),n.d(r,{default:()=>d});var a=n(3480),i=n(8667),s=n(6435),o=n(358);require("socks-proxy-agent");let l=e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ru:"Russian",ja:"Japanese",ko:"Korean",zh:"Chinese",ar:"Arabic",hi:"Hindi",tr:"Turkish",pl:"Polish",nl:"Dutch",sv:"Swedish",da:"Danish",no:"Norwegian",fi:"Finnish",cs:"Czech",hu:"Hungarian",ro:"Romanian",bg:"Bulgarian",hr:"Croatian",sk:"Slovak",sl:"Slovenian",et:"Estonian",lv:"Latvian",lt:"Lithuanian",uk:"Ukrainian",el:"Greek",he:"Hebrew",th:"Thai",vi:"Vietnamese",id:"Indonesian",ms:"Malay",tl:"Filipino",sw:"Swahili",af:"Afrikaans"})[e]||e.toUpperCase(),u=o.createProxyAgent(process.env.SOCKET_URL),d=async(e,t)=>{if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return void t.status(200).end();if("GET"!==e.method)return void t.status(405).json({error:"Method not allowed"});try{let{videoId:n}=e.query;if(!n)return t.status(400).json({error:"Video ID is required"});console.log(`Fetching subtitle languages for video: ${n}`);let r=await o.getInfo(`https://www.youtube.com/watch?v=${n}`,{agent:u}),a=[];if(r.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks&&r.player_response.captions.playerCaptionsTracklistRenderer.captionTracks.forEach(e=>{let t=e.languageCode,n=e.name?.simpleText||l(t),r="asr"===e.kind;a.push({code:t,name:n,isAutoGenerated:r})}),0===a.length)return t.status(404).json({error:"No subtitles available for this video"});t.status(200).json({videoId:n,title:r.videoDetails?.title||"YouTube Video",thumbnail:r.videoDetails?.thumbnails?.[0]?.url||`https://img.youtube.com/vi/${n}/hqdefault.jpg`,languages:a})}catch(e){console.error("Error fetching subtitle languages:",e),t.status(500).json({error:"Failed to fetch subtitle languages",details:e instanceof Error?e.message:"Unknown error"})}},c=(0,s.M)(r,"default"),p=(0,s.M)(r,"config"),h=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/subtitles/languages/[videoId]",pathname:"/api/subtitles/languages/[videoId]",bundlePath:"",filename:""},userland:r})},3480:(e,t,n)=>{e.exports=n(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var n=t(t.s=2750);module.exports=n})();