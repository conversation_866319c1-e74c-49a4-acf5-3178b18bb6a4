"use strict";(()=>{var e={};e.id=639,e.ids=[639],e.modules={45:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>i});var o=r(9767);!function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}();var a=e([o]);let n=new(o=(a.then?(await a)():a)[0]).default(process.env.STRIPE_SECRET_KEY);async function i(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","POST, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let{priceId:r,userId:s,userEmail:o}=e.body;if(!r||!s||!o)return t.status(400).json({error:"Missing required fields"});let a=Object(function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:i}=await a.from("users").select("stripe_customer_id").eq("id",s).single(),c=i?.stripe_customer_id;c||(c=(await n.customers.create({email:o,metadata:{supabase_user_id:s}})).id,await a.from("users").update({stripe_customer_id:c}).eq("id",s));let u=await n.checkout.sessions.create({customer:c,payment_method_types:["card"],line_items:[{price:r,quantity:1}],mode:"subscription",success_url:`${e.headers.origin||"http://localhost:5173"}/dashboard?session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${e.headers.origin||"http://localhost:5173"}/pricing`,metadata:{user_id:s}});t.status(200).json({sessionId:u.id})}catch(e){console.error("Error creating checkout session:",e),t.status(500).json({error:"Failed to create checkout session"})}}s()}catch(e){s(e)}})},410:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>d,default:()=>u,routeModule:()=>l});var o=r(3480),a=r(8667),i=r(6435),n=r(45),c=e([n]);n=(c.then?(await c)():c)[0];let u=(0,i.M)(n,"default"),d=(0,i.M)(n,"config"),l=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/stripe/checkout",pathname:"/api/stripe/checkout",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=410);module.exports=r})();