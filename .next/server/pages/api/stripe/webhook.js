"use strict";(()=>{var e={};e.id=550,e.ids=[550],e.modules={262:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>d,default:()=>u,routeModule:()=>p});var s=r(3480),o=r(8667),n=r(6435),a=r(4100),c=e([a]);a=(c.then?(await c)():c)[0];let u=(0,n.M)(a,"default"),d=(0,n.M)(a,"config"),p=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/stripe/webhook",pathname:"/api/stripe/webhook",bundlePath:"",filename:""},userland:a});i()}catch(e){i(e)}})},3480:(e,t,r)=>{e.exports=r(5600)},4100:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>n});var s=r(9767);!function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}();var o=e([s]);let p=new(s=(o.then?(await o)():o)[0]).default(process.env.STRIPE_SECRET_KEY),_=process.env.STRIPE_WEBHOOK_SECRET;async function n(e,t){let r;if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let i=e.headers["stripe-signature"];try{r=p.webhooks.constructEvent(e.body,i,_)}catch(e){return console.error("Webhook signature verification failed:",e.message),t.status(400).json({error:"Webhook signature verification failed"})}let s=Object(function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}())();try{switch(r.type){case"checkout.session.completed":{let e=r.data.object,t=await p.subscriptions.retrieve(e.subscription);await a(s,t,e.metadata.user_id);break}case"customer.subscription.updated":{let e=r.data.object;await c(s,e);break}case"customer.subscription.deleted":{let e=r.data.object;await u(s,e);break}case"invoice.payment_succeeded":{let e=r.data.object;if(e.subscription){let t=await p.subscriptions.retrieve(e.subscription);await c(s,t)}break}case"invoice.payment_failed":{let e=r.data.object;if(e.subscription){let t=await p.subscriptions.retrieve(e.subscription);await c(s,t)}break}default:console.log(`Unhandled event type: ${r.type}`)}t.status(200).json({received:!0})}catch(e){console.error("Error processing webhook:",e),t.status(500).json({error:"Webhook processing failed"})}}async function a(e,t,r){let i=t.items.data[0].price.id,s=d(i),o={user_id:r,stripe_subscription_id:t.id,stripe_customer_id:t.customer,status:t.status,tier:s,current_period_start:new Date(1e3*t.current_period_start).toISOString(),current_period_end:new Date(1e3*t.current_period_end).toISOString(),cancel_at_period_end:t.cancel_at_period_end},{error:n}=await e.from("subscriptions").upsert(o,{onConflict:"stripe_subscription_id"});if(n)throw console.error("Error creating subscription:",n),n;await e.from("usage_stats").upsert({user_id:r,videos_extracted_this_month:0,last_reset_date:new Date().toISOString()},{onConflict:"user_id"})}async function c(e,t){let r=t.items.data[0].price.id,i=d(r),s={status:t.status,tier:i,current_period_start:new Date(1e3*t.current_period_start).toISOString(),current_period_end:new Date(1e3*t.current_period_end).toISOString(),cancel_at_period_end:t.cancel_at_period_end,updated_at:new Date().toISOString()},{error:o}=await e.from("subscriptions").update(s).eq("stripe_subscription_id",t.id);if(o)throw console.error("Error updating subscription:",o),o}async function u(e,t){let{error:r}=await e.from("subscriptions").update({status:"canceled",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",t.id);if(r)throw console.error("Error deleting subscription:",r),r}function d(e){return({[process.env.STRIPE_STARTER_PRICE_ID]:"starter",[process.env.STRIPE_PRO_PRICE_ID]:"pro",[process.env.STRIPE_PREMIUM_PRICE_ID]:"premium"})[e]||"starter"}i()}catch(e){i(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=262);module.exports=r})();