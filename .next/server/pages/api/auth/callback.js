"use strict";(()=>{var e={};e.id=745,e.ids=[745],e.modules={690:(e,t,r)=>{r.r(t),r.d(t,{config:()=>l,default:()=>c,routeModule:()=>u});var o={};r.r(o),r.d(o,{default:()=>i});var n=r(3480),a=r(8667),s=r(6435);async function i(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let{code:r,state:o,error:n}=e.query;if(n)return t.redirect(`${process.env.SITE_URL||"http://localhost:5173"}/?error=${encodeURIComponent(n)}`);if(r)return t.redirect(`${process.env.SITE_URL||"http://localhost:5173"}/dashboard`);t.redirect(`${process.env.SITE_URL||"http://localhost:5173"}/`)}catch(e){console.error("Auth callback error:",e),t.redirect(`${process.env.SITE_URL||"http://localhost:5173"}/?error=auth_error`)}}let c=(0,s.M)(o,"default"),l=(0,s.M)(o,"config"),u=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/auth/callback",pathname:"/api/auth/callback",bundlePath:"",filename:""},userland:o})},3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=690);module.exports=r})();