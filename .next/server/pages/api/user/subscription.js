"use strict";(()=>{var e={};e.id=758,e.ids=[758],e.modules={3480:(e,t,r)=>{e.exports=r(5600)},3716:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>c,default:()=>d,routeModule:()=>l});var n=r(3480),o=r(8667),i=r(6435),a=r(4520),u=e([a]);a=(u.then?(await u)():u)[0];let d=(0,i.M)(a,"default"),c=(0,i.M)(a,"config"),l=new n.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/user/subscription",pathname:"/api/user/subscription",bundlePath:"",filename:""},userland:a});s()}catch(e){s(e)}})},4520:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>i});var n=r(9767);!function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}();var o=e([n]);let a=new(n=(o.then?(await o)():o)[0]).default(process.env.STRIPE_SECRET_KEY);async function i(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();let r=Object(function(){var e=Error("Cannot find module '../../src/lib/supabase.js'");throw e.code="MODULE_NOT_FOUND",e}())();try{if("GET"===e.method){let{userId:s}=e.query;if(!s)return t.status(400).json({error:"User ID required"});let{data:n,error:o}=await r.from("subscriptions").select("*").eq("user_id",s).eq("status","active").single();if(o&&"PGRST116"!==o.code)throw o;let{data:i,error:a}=await r.from("usage_stats").select("*").eq("user_id",s).single();if(a&&"PGRST116"!==a.code)throw a;t.status(200).json({subscription:n,usage:i})}else if("DELETE"===e.method){let{subscriptionId:s}=e.body;if(!s)return t.status(400).json({error:"Subscription ID required"});let n=await a.subscriptions.update(s,{cancel_at_period_end:!0}),{error:o}=await r.from("subscriptions").update({cancel_at_period_end:!0,updated_at:new Date().toISOString()}).eq("stripe_subscription_id",s);if(o)throw o;t.status(200).json({success:!0,subscription:n})}else t.status(405).json({error:"Method not allowed"})}catch(e){console.error("Error in subscription endpoint:",e),t.status(500).json({error:"Internal server error"})}}s()}catch(e){s(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=3716);module.exports=r})();