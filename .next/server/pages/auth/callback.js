(()=>{var e={};e.id=620,e.ids=[220,620,636],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},417:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(3939);let a=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",i=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key";if((!a||!i||a.includes("placeholder"))&&1)throw Error("Missing Supabase environment variables");let o=(0,s.createClient)(a,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},798:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{A:()=>c,O:()=>d});var a=r(8732),i=r(2015),o=r(417),n=r(2893),l=e([n]);n=(l.then?(await l)():l)[0];let u=(0,i.createContext)(void 0),c=()=>{let e=(0,i.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,i.useState)(null),[s,l]=(0,i.useState)(!0),[c,d]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await o.N.auth.getSession();t?(console.error("Error getting session:",t),d(t.message)):r(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),d("Failed to get session")}finally{l(!1)}})();let{data:{subscription:e}}=o.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(r(t?.user||null),d(null),t?.user&&await p(t.user)):"SIGNED_OUT"===e&&(r(null),d(null)),l(!1)});return()=>e.unsubscribe()},[]);let p=async e=>{try{let{error:t}=await o.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},g=async()=>{try{l(!0),d(null);let{error:e}=await o.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(d(e.message),n.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),d("Failed to sign in"),n.default.error("Failed to sign in with Google")}finally{l(!1)}},h=async()=>{try{l(!0),d(null);let{error:e}=await o.N.auth.signOut();e?(d(e.message),n.default.error("Failed to sign out")):n.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),d("Failed to sign out"),n.default.error("Failed to sign out")}finally{l(!1)}},m=async()=>{try{let{data:{user:e},error:t}=await o.N.auth.getUser();t?d(t.message):r(e)}catch(e){console.error("Error refreshing user:",e),d("Failed to refresh user")}};return(0,a.jsx)(u.Provider,{value:{user:t,loading:s,error:c,signInWithGoogle:g,signOut:h,refreshUser:m},children:e})};s()}catch(e){s(e)}})},2015:e=>{"use strict";e.exports=require("react")},2326:e=>{"use strict";e.exports=require("react-dom")},2386:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>l});var a=r(8732),i=r(2893),o=r(798);r(2768);var n=e([i,o]);function l({Component:e,pageProps:t}){return(0,a.jsxs)(o.O,{children:[(0,a.jsx)(e,{...t}),(0,a.jsx)(i.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[i,o]=n.then?(await n)():n,s()}catch(e){s(e)}})},2768:()=>{},2893:e=>{"use strict";e.exports=import("react-hot-toast")},3873:e=>{"use strict";e.exports=require("path")},3939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},4075:e=>{"use strict";e.exports=require("zlib")},7910:e=>{"use strict";e.exports=require("stream")},7957:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>f,default:()=>p,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>g,reportWebVitals:()=>S,routeModule:()=>y,unstable_getServerProps:()=>_,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>b});var a=r(3885),i=r(237),o=r(1413),n=r(9616),l=r.n(n),u=r(2386),c=r(8346),d=e([u]);u=(d.then?(await d)():d)[0];let p=(0,o.M)(c,"default"),g=(0,o.M)(c,"getStaticProps"),h=(0,o.M)(c,"getStaticPaths"),m=(0,o.M)(c,"getServerSideProps"),f=(0,o.M)(c,"config"),S=(0,o.M)(c,"reportWebVitals"),b=(0,o.M)(c,"unstable_getStaticProps"),v=(0,o.M)(c,"unstable_getStaticPaths"),x=(0,o.M)(c,"unstable_getStaticParams"),_=(0,o.M)(c,"unstable_getServerProps"),P=(0,o.M)(c,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/auth/callback",pathname:"/auth/callback",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:c});s()}catch(e){s(e)}})},8346:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(8732);r(2015);var a=r(4233);function i(){return(0,a.useRouter)(),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-slate-900",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-slate-300",children:"Completing authentication..."})]})})}r(417)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9021:e=>{"use strict";e.exports=require("fs")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[616,441],()=>r(7957));module.exports=s})();