"use strict";(()=>{var e={};e.id=18,e.ids=[18,220],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1680:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1777:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>g,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>p,reportWebVitals:()=>b,routeModule:()=>N,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>f});var r=s(3885),i=s(237),l=s(1413),n=s(9616),o=s.n(n),c=s(2386),d=s(9864),m=e([c,d]);[c,d]=m.then?(await m)():m;let x=(0,l.M)(d,"default"),p=(0,l.M)(d,"getStaticProps"),u=(0,l.M)(d,"getStaticPaths"),h=(0,l.M)(d,"getServerSideProps"),g=(0,l.M)(d,"config"),b=(0,l.M)(d,"reportWebVitals"),f=(0,l.M)(d,"unstable_getStaticProps"),y=(0,l.M)(d,"unstable_getStaticPaths"),j=(0,l.M)(d,"unstable_getStaticParams"),v=(0,l.M)(d,"unstable_getServerProps"),w=(0,l.M)(d,"unstable_getServerSideProps"),N=new r.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/pricing",pathname:"/pricing",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:d});a()}catch(e){a(e)}})},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2439:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3220:e=>{e.exports=import("framer-motion")},3517:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>j});var r=s(8732),i=s(2015),l=s.n(i),n=s(3220),o=s(7459),c=s(2237),d=s(6786),m=s(6708),x=s(2439),p=s(2312),u=s(7089),h=s(2796),g=s(798),b=s(849),f=s(2893),y=e([n,o,c,d,g,b,f]);[n,o,c,d,g,b,f]=y.then?(await y)():y;let j=({tier:e,isCurrentPlan:t=!1,onSelectPlan:s})=>{let{user:a}=(0,g.A)(),{createCheckoutSession:i}=(0,b.R)(),[y,j]=l().useState(!1),v=h.D[e],w=async()=>{if(!a)return void f.default.error("Please sign in to subscribe");if(t)return void(0,f.default)("This is your current plan");try{j(!0);let t=await i(e),s=await h.t;if(!s)throw Error("Stripe failed to load");let{error:a}=await s.redirectToCheckout({sessionId:t});if(a)throw a}catch(e){console.error("Error selecting plan:",e),f.default.error("Failed to start checkout process")}finally{j(!1)}};return(0,r.jsx)(n.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"relative",children:(0,r.jsxs)(c.Zp,{className:`relative overflow-hidden ${v.popular?"border-purple-500 shadow-lg shadow-purple-500/20":"border-slate-700"} bg-slate-800/80 backdrop-blur-sm`,children:[v.popular&&(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium",children:"Most Popular"})}),(0,r.jsxs)(c.aR,{className:v.popular?"pt-12":"pt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(e){case"starter":return(0,r.jsx)(m.A,{className:"w-6 h-6 text-blue-500"});case"pro":return(0,r.jsx)(x.A,{className:"w-6 h-6 text-purple-500"});case"premium":return(0,r.jsx)(p.A,{className:"w-6 h-6 text-yellow-500"});default:return(0,r.jsx)(m.A,{className:"w-6 h-6"})}})(),(0,r.jsx)(c.ZB,{className:"text-white",children:v.name})]}),t&&(0,r.jsx)(d.E,{variant:"secondary",className:"bg-green-600 text-white",children:"Current Plan"})]}),(0,r.jsx)(c.BT,{className:"text-gray-300",children:(0,r.jsxs)("div",{className:"flex items-baseline gap-1",children:[(0,r.jsxs)("span",{className:"text-3xl font-bold text-white",children:["$",v.price]}),(0,r.jsx)("span",{className:"text-gray-400",children:"/month"})]})})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("ul",{className:"space-y-3",children:v.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:e})]},t))}),(0,r.jsxs)("div",{className:"pt-4 border-t border-slate-700",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Usage Limits"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-gray-400",children:[(0,r.jsxs)("div",{children:["Videos per month: ",-1===v.limits.videosPerMonth?"Unlimited":v.limits.videosPerMonth]}),(0,r.jsxs)("div",{children:["Max video length: ",-1===v.limits.maxVideoLength?"Unlimited":`${v.limits.maxVideoLength} minutes`]})]})]})]}),(0,r.jsx)(c.wL,{children:(0,r.jsx)(o.$,{onClick:w,disabled:y||t,className:`w-full ${v.popular?"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700":"bg-slate-700 hover:bg-slate-600"} text-white`,children:y?"Processing...":t?"Current Plan":`Choose ${v.name}`})}),(0,r.jsx)("div",{className:`absolute inset-0 bg-gradient-to-br ${(()=>{switch(e){case"starter":return"from-blue-500 to-cyan-500";case"pro":return"from-purple-500 to-pink-500";case"premium":return"from-yellow-500 to-orange-500";default:return"from-gray-500 to-gray-600"}})()} opacity-5 pointer-events-none`})]})})};a()}catch(e){a(e)}})},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},5979:e=>{e.exports=import("tailwind-merge")},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},6708:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},6786:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{E:()=>o});var r=s(8732);s(2015);var i=s(8938),l=s(3678),n=e([i,l]);[i,l]=n.then?(await n)():n;let c=(0,i.cva)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,l.cn)(c({variant:t}),e),...s})}a()}catch(e){a(e)}})},7089:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7172:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>u});var r=s(8732);s(2015);var i=s(3220),l=s(3517),n=s(2796),o=s(849),c=s(798),d=s(3996),m=s(1680),x=s(7459),p=e([i,l,o,c,d,x]);[i,l,o,c,d,x]=p.then?(await p)():p;let u=({onBack:e})=>{let{user:t}=(0,c.A)(),{subscription:s}=(0,o.R)();return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-12",children:[e&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"mb-8",children:(0,r.jsxs)(x.$,{onClick:e,variant:"ghost",className:"text-gray-300 hover:text-white",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,r.jsxs)(i.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6",children:"Choose Your Plan"}),(0,r.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto mb-8",children:"Unlock the full power of YouTube subtitle extraction with our professional plans. Start with any plan and upgrade anytime."}),!t&&(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsx)(d.A,{size:"lg",className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Sign in to Get Started"})})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:Object.keys(n.D).map((e,t)=>(0,r.jsx)(i.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},children:(0,r.jsx)(l.A,{tier:e,isCurrentPlan:s?.tier===e&&s?.status==="active"})},e))}),(0,r.jsxs)(i.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"bg-slate-800/80 backdrop-blur-sm rounded-xl p-8 border border-slate-700",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Why Choose Our Premium Plans?"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"⚡"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Lightning Fast"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Extract subtitles in seconds with our optimized processing pipeline"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83C\uDFAF"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"High Accuracy"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Advanced AI processing ensures the highest quality subtitle extraction"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83D\uDD12"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Secure & Private"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Your data is processed securely and never stored on our servers"})]})]})]}),(0,r.jsxs)(i.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"Frequently Asked Questions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-left",children:[(0,r.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Can I change plans anytime?"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately."})]}),(0,r.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"What payment methods do you accept?"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"We accept all major credit cards, debit cards, and digital wallets through Stripe."})]}),(0,r.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Is there a free trial?"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"All plans come with a 7-day money-back guarantee. Cancel anytime within the first week for a full refund."})]}),(0,r.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Do you offer refunds?"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Yes, we offer a 7-day money-back guarantee on all plans. Contact support for assistance."})]})]})]})]})})};a()}catch(e){a(e)}})},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9640:e=>{e.exports=import("@radix-ui/react-slot")},9864:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var r=s(8732),i=s(9788),l=s.n(i),n=s(4233),o=s(8405),c=s(5027),d=s(7172),m=e([o,c,d]);function x(){let e=(0,n.useRouter)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l(),{children:[(0,r.jsx)("title",{children:"Pricing - DownloadYTSubtitles"}),(0,r.jsx)("meta",{name:"description",content:"Choose your plan for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,r.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,r.jsx)(o.A,{currentView:"pricing",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,r.jsx)(d.A,{onBack:()=>{e.push("/")}}),(0,r.jsx)(c.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[o,c,d]=m.then?(await m)():m,a()}catch(e){a(e)}})}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[616,441,975,312],()=>s(1777));module.exports=a})();