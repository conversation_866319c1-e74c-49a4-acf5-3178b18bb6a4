(()=>{var e={};e.id=636,e.ids=[636],e.modules={417:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var a=r(3939);let s=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",o=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key";if((!s||!o||s.includes("placeholder"))&&1)throw Error("Missing Supabase environment variables");let i=(0,a.createClient)(s,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},798:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>c,O:()=>d});var s=r(8732),o=r(2015),i=r(417),n=r(2893),l=e([n]);n=(l.then?(await l)():l)[0];let u=(0,o.createContext)(void 0),c=()=>{let e=(0,o.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(!0),[c,d]=(0,o.useState)(null);(0,o.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await i.N.auth.getSession();t?(console.error("Error getting session:",t),d(t.message)):r(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),d("Failed to get session")}finally{l(!1)}})();let{data:{subscription:e}}=i.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(r(t?.user||null),d(null),t?.user&&await h(t.user)):"SIGNED_OUT"===e&&(r(null),d(null)),l(!1)});return()=>e.unsubscribe()},[]);let h=async e=>{try{let{error:t}=await i.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},g=async()=>{try{l(!0),d(null);let{error:e}=await i.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(d(e.message),n.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),d("Failed to sign in"),n.default.error("Failed to sign in with Google")}finally{l(!1)}},p=async()=>{try{l(!0),d(null);let{error:e}=await i.N.auth.signOut();e?(d(e.message),n.default.error("Failed to sign out")):n.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),d("Failed to sign out"),n.default.error("Failed to sign out")}finally{l(!1)}},f=async()=>{try{let{data:{user:e},error:t}=await i.N.auth.getUser();t?d(t.message):r(e)}catch(e){console.error("Error refreshing user:",e),d("Failed to refresh user")}};return(0,s.jsx)(u.Provider,{value:{user:t,loading:a,error:c,signInWithGoogle:g,signOut:p,refreshUser:f},children:e})};a()}catch(e){a(e)}})},2015:e=>{"use strict";e.exports=require("react")},2386:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(8732),o=r(2893),i=r(798);r(2768);var n=e([o,i]);function l({Component:e,pageProps:t}){return(0,s.jsxs)(i.O,{children:[(0,s.jsx)(e,{...t}),(0,s.jsx)(o.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[o,i]=n.then?(await n)():n,a()}catch(e){a(e)}})},2768:()=>{},2893:e=>{"use strict";e.exports=import("react-hot-toast")},3939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")}};var t=require("../webpack-runtime.js");t.C(e);var r=t(t.s=2386);module.exports=r})();