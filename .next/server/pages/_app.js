/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./components/contexts/AuthContext.tsx":
/*!*********************************************!*\
  !*** ./components/contexts/AuthContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(pages-dir-node)/./lib/supabase.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting session:', error);\n                            setError(error.message);\n                        } else {\n                            setUser(session?.user || null);\n                        }\n                    } catch (err) {\n                        console.error('Error in getInitialSession:', err);\n                        setError('Failed to get session');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state changed:', event, session?.user?.email);\n                    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {\n                        setUser(session?.user || null);\n                        setError(null);\n                        // Create or update user profile\n                        if (session?.user) {\n                            await createOrUpdateUserProfile(session.user);\n                        }\n                    } else if (event === 'SIGNED_OUT') {\n                        setUser(null);\n                        setError(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const createOrUpdateUserProfile = async (user)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').upsert({\n                id: user.id,\n                email: user.email || '',\n                full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,\n                avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture || null,\n                updated_at: new Date().toISOString()\n            }, {\n                onConflict: 'id'\n            });\n            if (error) {\n                console.error('Error creating/updating user profile:', error);\n            }\n        } catch (err) {\n            console.error('Error in createOrUpdateUserProfile:', err);\n        }\n    };\n    const signInWithGoogle = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n            }\n        } catch (err) {\n            console.error('Error signing in with Google:', err);\n            setError('Failed to sign in');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Signed out successfully');\n            }\n        } catch (err) {\n            console.error('Error signing out:', err);\n            setError('Failed to sign out');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const { data: { user }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (error) {\n                setError(error.message);\n            } else {\n                setUser(user);\n            }\n        } catch (err) {\n            console.error('Error refreshing user:', err);\n            setError('Failed to refresh user');\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        signInWithGoogle,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/contexts/AuthContext.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';\n// Only throw error in production runtime (not during build)\nif ((!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) && \"development\" === 'production' && 0) {}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n    }\n});\n// Server-side client for API routes (Node.js environment)\nconst createServerSupabaseClient = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(process.env.NEXT_PUBLIC_SUPABASE_URL || supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/supabase.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: '#1e293b',\n                        color: '#f1f5f9',\n                        border: '1px solid #475569'\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzBDO0FBQ3dCO0FBQ25DO0FBRWhCLFNBQVNFLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBFQUFZQTs7MEJBQ1gsOERBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7OzswQkFDeEIsOERBQUNKLG9EQUFPQTtnQkFDTkssVUFBUztnQkFDVEMsY0FBYztvQkFDWkMsVUFBVTtvQkFDVkMsT0FBTzt3QkFDTEMsWUFBWTt3QkFDWkMsT0FBTzt3QkFDUEMsUUFBUTtvQkFDVjtnQkFDRjs7Ozs7Ozs7Ozs7O0FBSVIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL3BhZ2VzL19hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbXBvbmVudHMvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8VG9hc3RlclxuICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFlMjkzYicsXG4gICAgICAgICAgICBjb2xvcjogJyNmMWY1ZjknLFxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM0NzU1NjknLFxuICAgICAgICAgIH0sXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJBdXRoUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwb3NpdGlvbiIsInRvYXN0T3B0aW9ucyIsImR1cmF0aW9uIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJib3JkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(pages-dir-node)/./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();