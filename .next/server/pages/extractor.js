"use strict";(()=>{var e={};e.id=440,e.ids=[220,440],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},709:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1680:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3213:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3220:e=>{e.exports=import("framer-motion")},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4001:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>g,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>p,reportWebVitals:()=>b,routeModule:()=>N,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>y});var r=s(3885),l=s(237),i=s(1413),n=s(9616),c=s.n(n),o=s(2386),m=s(7862),d=e([o,m]);[o,m]=d.then?(await d)():d;let x=(0,i.M)(m,"default"),p=(0,i.M)(m,"getStaticProps"),u=(0,i.M)(m,"getStaticPaths"),h=(0,i.M)(m,"getServerSideProps"),g=(0,i.M)(m,"config"),b=(0,i.M)(m,"reportWebVitals"),y=(0,i.M)(m,"unstable_getStaticProps"),f=(0,i.M)(m,"unstable_getStaticPaths"),v=(0,i.M)(m,"unstable_getStaticParams"),w=(0,i.M)(m,"unstable_getServerProps"),j=(0,i.M)(m,"unstable_getServerSideProps"),N=new r.PagesRouteModule({definition:{kind:l.A.PAGES,page:"/extractor",pathname:"/extractor",bundlePath:"",filename:""},components:{App:o.default,Document:c()},userland:m});a()}catch(e){a(e)}})},4075:e=>{e.exports=require("zlib")},4447:(e,t,s)=>{function a(e){if(!e||"string"!=typeof e)return{isValid:!1,type:"invalid",url:e,error:"Please enter a valid URL"};let t=e.trim();for(let e of[/youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)/,/youtube\.com\/watch\?.*list=([a-zA-Z0-9_-]+)/])if(t.match(e))return{isValid:!1,type:"invalid",url:t,error:"Playlist support is coming soon. Please use a single video URL for now."};for(let e of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([a-zA-Z0-9_-]{11})/,/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/]){let s=t.match(e);if(s){let e=s[1];if(e&&/^[a-zA-Z0-9_-]{11}$/.test(e))return{isValid:!0,type:"video",videoId:e,url:t}}}return t.includes("youtube.com")||t.includes("youtu.be")?{isValid:!1,type:"invalid",url:t,error:"Invalid YouTube URL format. Please check the URL and try again."}:{isValid:!1,type:"invalid",url:t,error:"Please enter a valid YouTube video URL"}}s.d(t,{vj:()=>a})},5091:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},5134:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5979:e=>{e.exports=import("tailwind-merge")},6076:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{k:()=>o});var r=s(8732),l=s(2015),i=s(7947),n=s(3678),c=e([i,n]);[i,n]=c.then?(await c)():c;let o=l.forwardRef(({className:e,value:t,...s},a)=>(0,r.jsx)(i.Root,{ref:a,className:(0,n.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...s,children:(0,r.jsx)(i.Indicator,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));o.displayName=i.Root.displayName,a()}catch(e){a(e)}})},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},6414:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},6769:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{p:()=>c});var r=s(8732),l=s(2015),i=s(3678),n=e([i]);i=(n.then?(await n)():n)[0];let c=l.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));c.displayName="Input",a()}catch(e){a(e)}})},6786:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{E:()=>c});var r=s(8732);s(2015);var l=s(8938),i=s(3678),n=e([l,i]);[l,i]=n.then?(await n)():n;let o=(0,l.cva)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(o({variant:t}),e),...s})}a()}catch(e){a(e)}})},7290:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7862:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var r=s(8732),l=s(9788),i=s.n(l),n=s(4233),c=s(8405),o=s(5027),m=s(9356),d=e([c,o,m]);function x(){let e=(0,n.useRouter)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i(),{children:[(0,r.jsx)("title",{children:"Extract YouTube Subtitles - DownloadYTSubtitles"}),(0,r.jsx)("meta",{name:"description",content:"Extract and download YouTube subtitles in VTT and TXT formats. Fast, free, and easy to use."}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,r.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,r.jsx)(c.A,{currentView:"extractor",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,r.jsx)(m.A,{onBack:()=>{e.push("/")}}),(0,r.jsx)(o.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[c,o,m]=d.then?(await d)():d,a()}catch(e){a(e)}})},7910:e=>{e.exports=require("stream")},7947:e=>{e.exports=import("@radix-ui/react-progress")},7962:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9356:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>C});var r=s(8732),l=s(2015),i=s(3220),n=s(7459),c=s(6769),o=s(2237),m=s(6786),d=s(6076),x=s(1680),p=s(2312),u=s(7962),h=s(1335),g=s(3213),b=s(709),y=s(5134),f=s(3295),v=s(5091),w=s(6414),j=s(7290),N=s(4447),k=s(798),S=s(849),A=s(3996),$=s(2893),P=e([i,n,c,o,m,d,k,S,A,$]);[i,n,c,o,m,d,k,S,A,$]=P.then?(await P)():P;let C=({onBack:e})=>{let{user:t}=(0,k.A)(),{subscription:s,canExtractVideo:a,getRemainingExtractions:P}=(0,S.R)(),[C,M]=(0,l.useState)(""),[R,V]=(0,l.useState)(null),[L,U]=(0,l.useState)([]),[F,T]=(0,l.useState)("en"),[E,_]=(0,l.useState)(!1),[I,Z]=(0,l.useState)(!1),[z,q]=(0,l.useState)(0),[B,Y]=(0,l.useState)("input"),[W,D]=(0,l.useState)([]),[G,H]=(0,l.useState)(null),[O,Q]=(0,l.useState)(!0),[X,K]=(0,l.useState)("vtt"),[J,ee]=(0,l.useState)(""),et=(0,l.useMemo)(()=>J.trim()?L.filter(e=>e.name.toLowerCase().includes(J.toLowerCase())||e.code.toLowerCase().includes(J.toLowerCase())):L,[L,J]),es=e=>{M(e),e.trim()?(_(!0),setTimeout(()=>{let t=(0,N.vj)(e);V(t),_(!1),!t.isValid&&e.trim()&&$.default.error(t.error||"Invalid YouTube URL")},500)):(V(null),_(!1))},ea=async()=>{if(R?.isValid){Z(!0),q(0);try{let e=setInterval(()=>{q(e=>Math.min(e+10,90))},200),t=await fetch(`/api/subtitles/languages/${R.videoId}`),s=await t.json();clearInterval(e),q(100),t.ok?(U(s.languages||[]),H(s),Y("languages"),$.default.success(`Found ${s.languages?.length||0} available languages!`)):($.default.error(s.error||"Failed to get available languages"),Y("input"))}catch(e){$.default.error("Network error. Please try again"),Y("input")}finally{Z(!1),q(0)}}},er=async()=>{if(R?.isValid&&F){if(!t)return void $.default.error("Please sign in to extract subtitles");if(!a()){let e=P();0===e?$.default.error("You have reached your monthly extraction limit. Please upgrade your plan."):$.default.error("Please subscribe to a plan to extract subtitles");return}Z(!0),q(0),Y("extracting");try{let e=setInterval(()=>{q(e=>Math.min(e+10,90))},200),t=await fetch(`/api/subtitles/download/${R.videoId}-${F}`),s=await t.json();clearInterval(e),q(100),t.ok?(D(s.subtitles||[]),H(s),Y("preview"),$.default.success("Subtitles extracted successfully!")):($.default.error(s.error||"Failed to extract subtitles"),Y("languages"))}catch(e){$.default.error("Network error. Please try again."),console.error(e),Y("languages")}finally{Z(!1),q(0)}}},el=e=>{if(!W.length)return;let t="",s=`${G?.title?.replace(/[^a-z0-9]/gi,"_").toLowerCase()||"subtitles"}.${e}`;switch(e){case"vtt":t="WEBVTT\n\n",W.forEach((e,s)=>{let a=e=>{let t=String(e??"");if(t.includes(":")&&3===t.split(":").length)return t;let s=parseFloat(t);if(isNaN(s))return"00:00:00.000";let a=Math.floor(s/3600),r=Math.floor(s%3600/60),l=(s%60).toFixed(3);return`${String(a).padStart(2,"0")}:${String(r).padStart(2,"0")}:${l.padStart(6,"0")}`};console.log(e);let r=a(e.start),l=a(e.end);t+=`${s+1}
${r} --> ${l}
${e.text}

`});break;case"txt":t=`Title: ${G?.title||"Unknown"}
Video ID: ${R?.videoId||"Unknown"}
Channel: ${G?.uploader||G?.channel||"Unknown"}

---

`,W.forEach(e=>{let s=e=>{let t=parseFloat(e),s=Math.floor(t/60),a=Math.floor(t%60);return`${s}:${a.toString().padStart(2,"0")}`},a=s(e.start),r=s(e.end);t+=`[${a} - ${r}] ${e.text}

`})}let a=new Blob([t],{type:"text/plain"}),r=URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download=s,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(r),$.default.success(`Downloaded ${s}`)};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-4 sm:py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,r.jsxs)(i.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-6 sm:mb-8",children:[(0,r.jsxs)(n.$,{variant:"ghost",onClick:e,className:"text-white hover:text-purple-400 text-sm sm:text-base p-2 sm:p-3",children:[(0,r.jsx)(x.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),(0,r.jsx)("span",{className:"hidden xs:inline",children:"Back to Home"}),(0,r.jsx)("span",{className:"xs:hidden",children:"Back"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,r.jsx)("div",{className:`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${"input"===B?"bg-purple-500":"languages"===B||"extracting"===B||"preview"===B?"bg-green-500":"bg-gray-600"}`}),(0,r.jsx)("div",{className:`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${"languages"===B?"bg-purple-500":"extracting"===B||"preview"===B?"bg-green-500":"bg-gray-600"}`}),(0,r.jsx)("div",{className:`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${"extracting"===B?"bg-purple-500":"preview"===B?"bg-green-500":"bg-gray-600"}`}),(0,r.jsx)("div",{className:`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${"preview"===B?"bg-purple-500":"bg-gray-600"}`})]})]}),t&&s&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6",children:(0,r.jsx)(o.Zp,{className:"bg-slate-800/60 backdrop-blur-sm border-slate-700",children:(0,r.jsx)(o.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-yellow-500"}),(0,r.jsxs)("span",{className:"text-white font-medium capitalize",children:[s.tier," Plan"]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-300",children:-1===P()?"Unlimited extractions":`${P()} extractions remaining`})]})})})}),!t&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6",children:(0,r.jsx)(o.Zp,{className:"bg-slate-800/60 backdrop-blur-sm border-slate-700",children:(0,r.jsx)(o.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-orange-500"}),(0,r.jsx)("span",{className:"text-white font-medium",children:"Sign in required"})]}),(0,r.jsx)(A.A,{size:"sm",variant:"outline"})]})})})}),(0,r.jsxs)(i.AnimatePresence,{mode:"wait",children:["input"===B&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,r.jsxs)(o.aR,{className:"pb-4 sm:pb-6",children:[(0,r.jsx)(o.ZB,{className:"text-white text-xl sm:text-2xl",children:"Enter YouTube URL"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Paste a YouTube video URL to get started"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4 sm:space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.p,{placeholder:"https://www.youtube.com/watch?v=...",value:C,onChange:e=>es(e.target.value),className:"bg-slate-700 border-slate-600 text-white placeholder-gray-400 text-sm sm:text-base"}),E&&(0,r.jsxs)("div",{className:"flex items-center text-gray-400 text-xs sm:text-sm",children:[(0,r.jsx)(h.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin"}),"Validating URL..."]}),R&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center space-x-2 flex-wrap",children:R.isValid?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400"}),(0,r.jsx)("span",{className:"text-green-400 text-sm sm:text-base",children:"Valid YouTube Video"}),(0,r.jsxs)(m.E,{variant:"secondary",className:"bg-purple-600 text-white text-xs",children:[(0,r.jsx)(b.A,{className:"w-3 h-3 mr-1"})," Video"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-red-400"}),(0,r.jsx)("span",{className:"text-red-400 text-sm sm:text-base",children:R.error})]})})]}),(0,r.jsx)(n.$,{onClick:ea,disabled:!R?.isValid||I,className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base",children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin"}),(0,r.jsx)("span",{className:"hidden xs:inline",children:"Getting Languages..."}),(0,r.jsx)("span",{className:"xs:hidden",children:"Loading..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Get Available Languages"]})})]})]})},"input"),"languages"===B&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,r.jsxs)(o.aR,{className:"pb-4 sm:pb-6",children:[(0,r.jsx)(o.ZB,{className:"text-white text-xl sm:text-2xl",children:"Select Language"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Choose the subtitle language you want to extract"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4 sm:space-y-6",children:[G&&(0,r.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg",children:[(0,r.jsx)("img",{src:G.thumbnail,alt:G.title,className:"w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-white font-semibold text-sm sm:text-base truncate",children:G.title}),(0,r.jsxs)("p",{className:"text-gray-400 text-xs sm:text-sm",children:["Video • ",L.length," languages available"]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-white font-medium text-sm sm:text-base",children:"Available Languages:"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3 sm:w-4 sm:h-4"}),(0,r.jsx)(c.p,{placeholder:"Search languages...",value:J,onChange:e=>ee(e.target.value),className:"bg-slate-700 border-slate-600 text-white placeholder-gray-400 pl-9 sm:pl-10 text-sm sm:text-base"})]}),(0,r.jsx)("div",{className:"max-h-48 sm:max-h-64 overflow-y-auto bg-slate-700 rounded-lg border border-slate-600",children:0===et.length?(0,r.jsx)("div",{className:"p-3 sm:p-4 text-center text-gray-400 text-sm sm:text-base",children:"No languages found"}):et.map(e=>(0,r.jsx)("div",{onClick:()=>T(e.code),className:`p-3 cursor-pointer hover:bg-slate-600 transition-colors ${F===e.code?"bg-purple-600":""}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-white text-sm sm:text-base",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[e.isAutoGenerated&&(0,r.jsx)(m.E,{variant:"secondary",className:"bg-orange-600 text-white text-xs",children:"Auto"}),F===e.code&&(0,r.jsx)(g.A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-white"})]})]})},e.code))})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>Y("input"),className:"flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base",children:[(0,r.jsx)(x.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Back"]}),(0,r.jsxs)(n.$,{onClick:er,disabled:!F,className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base",children:[(0,r.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Extract Subtitles"]})]})]})]})},"languages"),"extracting"===B&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,r.jsxs)(o.aR,{className:"pb-4 sm:pb-6",children:[(0,r.jsx)(o.ZB,{className:"text-white text-xl sm:text-2xl",children:"Extracting Subtitles"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Please wait while we extract and process the subtitles"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4 sm:space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.motion.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"inline-block",children:(0,r.jsx)(h.A,{className:"w-12 h-12 sm:w-16 sm:h-16 text-purple-500"})}),(0,r.jsx)("p",{className:"text-white mt-3 sm:mt-4 text-base sm:text-lg",children:"Processing..."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs sm:text-sm",children:[(0,r.jsx)("span",{className:"text-gray-300",children:"Progress"}),(0,r.jsxs)("span",{className:"text-purple-400",children:[z,"%"]})]}),(0,r.jsx)(d.k,{value:z,className:"h-2"})]}),(0,r.jsxs)("div",{className:"text-center text-gray-400 text-xs sm:text-sm px-2",children:[z<30&&"Fetching video information...",z>=30&&z<60&&"Downloading subtitle data...",z>=60&&z<90&&"Processing and cleaning subtitles...",z>=90&&"Almost done..."]})]})]})},"extracting"),"preview"===B&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,r.jsxs)(o.aR,{className:"pb-4 sm:pb-6",children:[(0,r.jsx)(o.ZB,{className:"text-white text-xl sm:text-2xl",children:"Subtitles Ready!"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Preview and download your extracted subtitles"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4 sm:space-y-6",children:[G&&(0,r.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg",children:[(0,r.jsx)("img",{src:G.thumbnail,alt:G.title,className:"w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-white font-semibold text-sm sm:text-base truncate",children:G.title}),(0,r.jsxs)("p",{className:"text-gray-400 text-xs sm:text-sm",children:[W.length," subtitle entries • ",L.find(e=>e.code===F)?.name||"English"]})]}),(0,r.jsx)(g.A,{className:"w-6 h-6 sm:w-8 sm:h-8 text-green-400 flex-shrink-0"})]}),(0,r.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0",children:[(0,r.jsx)("label",{className:"text-white font-medium text-sm sm:text-base",children:"Select Format:"}),(0,r.jsx)("div",{className:"flex space-x-2",children:["vtt","txt"].map(e=>(0,r.jsx)(n.$,{variant:X===e?"default":"outline",size:"sm",onClick:()=>K(e),className:`text-xs sm:text-sm px-3 sm:px-4 ${X===e?"bg-purple-600 hover:bg-purple-700":"border-slate-600 text-black hover:bg-slate-700 hover:text-white"}`,children:e.toUpperCase()},e))})]}),(0,r.jsxs)(n.$,{onClick:()=>el(X),className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base",children:[(0,r.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Download ",X.toUpperCase()," Format"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0",children:[(0,r.jsxs)("label",{className:"text-white font-medium text-sm sm:text-base",children:["Preview (",X.toUpperCase(),"):"]}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>Q(!O),className:"border-slate-600 text-black hover:bg-slate-700 hover:text-white text-xs sm:text-sm self-start sm:self-auto",children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"})," Hide"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"})," Show"]})})]}),O&&(0,r.jsx)(i.motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"max-h-64 sm:max-h-96 overflow-y-auto bg-slate-900 rounded-lg p-3 sm:p-4 border border-slate-600",children:(0,r.jsx)("pre",{className:"text-xs sm:text-sm text-white whitespace-pre-wrap font-mono",children:(e=>{if(!W.length)return"";switch(e){case"vtt":let t="WEBVTT\n\n";return W.forEach((e,s)=>{let a=e=>{let t=String(e??"");if(t.includes(":")&&3===t.split(":").length)return t;let s=parseFloat(t);if(isNaN(s))return"00:00:00.000";let a=Math.floor(s/3600),r=Math.floor(s%3600/60),l=(s%60).toFixed(3);return`${String(a).padStart(2,"0")}:${String(r).padStart(2,"0")}:${l.padStart(6,"0")}`},r=a(e.start),l=a(e.end);t+=`${s+1}
${r} --> ${l}
${e.text}

`}),t;case"txt":let s=`Title: ${G?.title||"Unknown"}
`;return s+=`Video ID: ${R?.videoId||"Unknown"}
Channel: ${G?.uploader||G?.channel||"Unknown"}

---

`,W.forEach(e=>{let t=e=>{let t=parseFloat(e),s=Math.floor(t/60),a=Math.floor(t%60);return`${s}:${a.toString().padStart(2,"0")}`},a=t(e.start),r=t(e.end);s+=`[${a} - ${r}] ${e.text}

`}),s;default:return""}})(X)})})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>{M(""),V(null),U([]),T("en"),Y("input"),D([]),H(null),Q(!1),K("vtt")},className:"flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base",children:"Extract Another"}),(0,r.jsx)(n.$,{onClick:()=>Y("input"),className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base",children:"Start Over"})]})]})]})},"preview")]})]})})};a()}catch(e){a(e)}})},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[616,441,975,312],()=>s(4001));module.exports=a})();