"use strict";(()=>{var e={};e.id=736,e.ids=[220,736],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1680:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3220:e=>{e.exports=import("framer-motion")},3470:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>u});var i=a(8732),r=a(9788),o=a.n(r),c=a(4233),n=a(8405),l=a(5027),d=a(5581),p=e([n,l,d]);function u(){let e=(0,c.useRouter)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(o(),{children:[(0,i.jsx)("title",{children:"Privacy Policy - DownloadYTSubtitles"}),(0,i.jsx)("meta",{name:"description",content:"Privacy Policy for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,i.jsx)(n.A,{currentView:"privacy",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,i.jsx)(d.A,{onBack:()=>{e.push("/")}}),(0,i.jsx)(l.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[n,l,d]=p.then?(await p)():p,s()}catch(e){s(e)}})},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},5017:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>y,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>v,routeModule:()=>N,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>g});var i=a(3885),r=a(237),o=a(1413),c=a(9616),n=a.n(c),l=a(2386),d=a(3470),p=e([l,d]);[l,d]=p.then?(await p)():p;let u=(0,o.M)(d,"default"),m=(0,o.M)(d,"getStaticProps"),h=(0,o.M)(d,"getStaticPaths"),y=(0,o.M)(d,"getServerSideProps"),x=(0,o.M)(d,"config"),v=(0,o.M)(d,"reportWebVitals"),g=(0,o.M)(d,"unstable_getStaticProps"),b=(0,o.M)(d,"unstable_getStaticPaths"),w=(0,o.M)(d,"unstable_getStaticParams"),j=(0,o.M)(d,"unstable_getServerProps"),f=(0,o.M)(d,"unstable_getServerSideProps"),N=new i.PagesRouteModule({definition:{kind:r.A.PAGES,page:"/privacy",pathname:"/privacy",bundlePath:"",filename:""},components:{App:l.default,Document:n()},userland:d});s()}catch(e){s(e)}})},5581:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{A:()=>y});var i=a(8732),r=a(3220),o=a(2237),c=a(7459),n=a(6e3),l=a(7962),d=a(6596),p=a(5974),u=a(7290),m=a(1680),h=e([r,o,c]);[r,o,c]=h.then?(await h)():h;let y=({onBack:e})=>{let t=[{title:"Information We Collect",icon:(0,i.jsx)(n.A,{className:"w-5 h-5"}),content:["We do NOT collect any personal information from users.","We do NOT require registration, login, or any form of account creation.","We do NOT store YouTube URLs, video content, or extracted subtitle data.","We do NOT use cookies for tracking or analytics purposes.","The only data processed is the YouTube URL you provide, which is used temporarily to extract subtitles and then immediately discarded."]},{title:"How We Process Data",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["When you submit a YouTube URL, our server temporarily processes it to extract publicly available subtitle data.","All processing happens in real-time with no data retention.","Subtitle extraction uses YouTube's public API endpoints that are already accessible.","No video content is downloaded, stored, or cached on our servers.","All temporary data is immediately purged after processing is complete."]},{title:"Data Storage and Retention",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),content:["We do NOT store any user data, URLs, or extracted content.","No databases are used to retain user information.","All processing is stateless and temporary.","Server logs may contain basic technical information (IP addresses, timestamps) for security purposes only.","Any technical logs are automatically purged within 24 hours."]},{title:"Third-Party Services",icon:(0,i.jsx)(p.A,{className:"w-5 h-5"}),content:["We interact with YouTube's public APIs to extract subtitle data.","We do not share any data with third-party analytics or tracking services.","No external advertising networks or data brokers are used.","The service operates independently without external data sharing."]},{title:"Your Privacy Rights",icon:(0,i.jsx)(u.A,{className:"w-5 h-5"}),content:["Since we don't collect personal data, there's no personal data to access, modify, or delete.","You can use our service completely anonymously.","No account creation means no data tied to your identity.","You have complete control over what URLs you submit for processing."]},{title:"Security Measures",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),content:["All connections to our service use HTTPS encryption.","Server infrastructure follows industry-standard security practices.","No sensitive data is stored, reducing security risks.","Regular security updates and monitoring are maintained."]},{title:"Children's Privacy",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),content:["Our service does not knowingly collect information from children under 13.","Since no personal information is collected from any users, this includes children.","Parents can safely allow children to use this service as no data collection occurs."]},{title:"International Users",icon:(0,i.jsx)(p.A,{className:"w-5 h-5"}),content:["This service can be used globally without data residency concerns.","Since no personal data is collected, GDPR and similar regulations are not applicable.","Users from all countries can use the service with the same privacy protections."]},{title:"Changes to Privacy Policy",icon:(0,i.jsx)(u.A,{className:"w-5 h-5"}),content:["Any changes to this privacy policy will be posted on this page.","We will update the 'Last Updated' date when changes are made.","Continued use of the service constitutes acceptance of any changes.","We are committed to maintaining our no-data-collection approach."]},{title:"Contact Us",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),content:["If you have questions about this Privacy Policy, please contact us through our support channels.","We are committed to transparency about our privacy practices.","We will respond to legitimate privacy inquiries promptly."]}];return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)(c.$,{variant:"ghost",onClick:e,className:"text-white hover:text-purple-400",children:[(0,i.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,i.jsx)("div",{className:"text-right",children:(0,i.jsxs)("p",{className:"text-gray-400 text-sm",children:["Last updated: ",new Date().toLocaleDateString()]})})]}),(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center mb-12",children:[(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Privacy Policy"}),(0,i.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Your privacy is important to us. This policy explains our privacy practices for YouTube Subtitle Extractor."})]}),(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:(0,i.jsx)(o.Zp,{className:"bg-green-900/20 border-green-500/30",children:(0,i.jsx)(o.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)(d.A,{className:"w-8 h-8 text-green-400 flex-shrink-0 mt-1"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-2",children:"Privacy-First Approach"}),(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("strong",{children:"We collect ZERO personal data."})," No registration, no tracking, no data storage. Your privacy is completely protected because we simply don't collect any information about you."]})]})]})})})}),(0,i.jsx)("div",{className:"space-y-6",children:t.map((e,t)=>(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(t+3)},children:(0,i.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,i.jsx)(o.aR,{children:(0,i.jsxs)(o.ZB,{className:"text-white flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,i.jsx)("span",{children:e.title})]})}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:e.content.map((e,t)=>(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e},t))})})]})},t))}),(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,i.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,i.jsxs)(o.Wu,{className:"p-6",children:[(0,i.jsx)("p",{className:"text-gray-300 mb-4",children:"This Privacy Policy demonstrates our commitment to protecting your privacy through a no-data-collection approach. You can use our service with complete confidence."}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["This policy is effective as of ",new Date().toLocaleDateString()," and will remain in effect except with respect to any changes in its provisions in the future."]})]})})})]})})};s()}catch(e){s(e)}})},5974:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5979:e=>{e.exports=import("tailwind-merge")},6e3:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},7290:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7910:e=>{e.exports=require("stream")},7962:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[616,441,975,312],()=>a(5017));module.exports=s})();