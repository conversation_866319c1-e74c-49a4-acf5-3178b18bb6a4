"use strict";(()=>{var e={};e.id=332,e.ids=[220,332],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},709:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1370:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>u});var i=s(8732),l=s(9788),o=s.n(l),r=s(4233),n=s(8405),c=s(5027),d=s(1552),m=e([n,c,d]);function u(){let e=(0,r.useRouter)(),t=t=>{e.push(`/${t}`)};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(o(),{children:[(0,i.jsx)("title",{children:"Download YouTube Subtitles - Free YT Caption Extractor - DownloadYTSubtitles"}),(0,i.jsx)("meta",{name:"description",content:"Free YouTube subtitle downloader and transcript extractor. Download YT captions in VTT and TXT formats from auto-generated and manual subtitles."}),(0,i.jsx)("meta",{name:"keywords",content:"YouTube subtitles, download YouTube captions, YouTube transcript extractor, YT subtitles, video captions"}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,i.jsx)(n.A,{currentView:"landing",onNavigate:t,onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,i.jsx)(d.A,{onGetStarted:()=>{e.push("/extractor")},onNavigateToPricing:()=>t("pricing")}),(0,i.jsx)(c.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[n,c,d]=m.then?(await m)():m,a()}catch(e){a(e)}})},1552:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>f});var i=s(8732),l=s(3220),o=s(709),r=s(5974),n=s(6708),c=s(3295),d=s(6596),m=s(7637),u=s(2439),p=s(2312),x=s(7139),h=s(7459),b=s(2237),g=s(5154),y=s(5972),w=s(2796),v=e([l,h,b,g,y]);[l,h,b,g,y]=v.then?(await v)():v;let f=({onGetStarted:e,onNavigateToPricing:t})=>{let s=[{icon:(0,i.jsx)(o.A,{className:"w-8 h-8"}),title:"YouTube Video Support",description:"Download subtitles from any YouTube video with available captions or auto-generated transcripts."},{icon:(0,i.jsx)(r.A,{className:"w-8 h-8"}),title:"70+ Languages Supported",description:"Extract YouTube captions in multiple languages including English, Spanish, French, German, and more."},{icon:(0,i.jsx)(n.A,{className:"w-8 h-8"}),title:"Auto-Generated Captions",description:"Advanced processing of YouTube's AI-generated subtitles with automatic cleaning and formatting."},{icon:(0,i.jsx)(c.A,{className:"w-8 h-8"}),title:"VTT & TXT Formats",description:"Download YouTube subtitles in VTT format for video players and web, or TXT with metadata for reading."},{icon:(0,i.jsx)(d.A,{className:"w-8 h-8"}),title:"Privacy Protected",description:"No data stored on our servers. YouTube subtitle extraction happens in real-time without tracking."},{icon:(0,i.jsx)(m.A,{className:"w-8 h-8"}),title:"Professional Plans",description:"Choose from flexible pricing plans designed for individuals, teams, and businesses of all sizes."}];return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:[(0,i.jsxs)("section",{className:"relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20"}),(0,i.jsx)("div",{className:"relative max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 pt-12 sm:pt-20 pb-12 sm:pb-16",children:(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[(0,i.jsxs)(l.motion.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight",children:["Download YouTube",(0,i.jsxs)("span",{className:"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:[" ","Subtitles"]})]}),(0,i.jsx)(l.motion.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-2",children:"Free YouTube subtitle downloader and transcript extractor. Get YT captions in VTT and TXT formats. Export YouTube transcripts from auto-generated and manual subtitles in 70+ languages."}),(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center",children:[(0,i.jsxs)(h.$,{onClick:e,size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,i.jsx)(o.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-2"}),"Get Started"]}),(0,i.jsx)(h.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full transition-all duration-300",onClick:()=>document.getElementById("features")?.scrollIntoView({behavior:"smooth"}),children:"Learn More"})]})]})})]}),(0,i.jsx)("section",{id:"features",className:"py-12 sm:py-20 bg-slate-800/50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Powerful Features"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Everything you need to extract and process YouTube subtitles professionally"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8",children:s.map((e,t)=>(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},whileHover:{scale:1.02},className:"bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-slate-700 hover:border-purple-500 transition-all duration-300",children:[(0,i.jsx)("div",{className:"text-purple-400 mb-3 sm:mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-sm sm:text-base text-gray-300",children:e.description})]},t))})]})}),(0,i.jsx)("section",{className:"py-12 sm:py-20",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Perfect For Everyone"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"From content creators to researchers, our tool serves diverse needs"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8",children:[{title:"Content Creators",description:"Extract subtitles for video editing, translation, or accessibility compliance.",gradient:"from-purple-500 to-pink-500"},{title:"Students & Researchers",description:"Get transcripts from educational videos for note-taking and research.",gradient:"from-blue-500 to-cyan-500"},{title:"Language Learners",description:"Study foreign languages with accurate subtitles and translations.",gradient:"from-green-500 to-emerald-500"},{title:"Accessibility Teams",description:"Create accessible content with properly formatted subtitle files.",gradient:"from-orange-500 to-red-500"}].map((e,t)=>(0,i.jsxs)(l.motion.div,{initial:{opacity:0,x:t%2==0?-20:20},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2*t},viewport:{once:!0},className:"relative overflow-hidden rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 p-4 sm:p-8 hover:border-purple-500 transition-all duration-300",children:[(0,i.jsx)("div",{className:`absolute inset-0 bg-gradient-to-r ${e.gradient} opacity-10`}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4",children:e.title}),(0,i.jsx)("p",{className:"text-gray-300 text-base sm:text-lg",children:e.description})]})]},t))})]})}),(0,i.jsx)(g.A,{}),(0,i.jsx)("section",{id:"faq",className:"py-12 sm:py-20 bg-slate-800/30",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Frequently Asked Questions"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Everything you need to know about downloading YouTube subtitles"})]}),(0,i.jsx)(y.A,{showHeader:!1})]})}),(0,i.jsx)("section",{className:"py-12 sm:py-20 bg-slate-800/50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6",children:"Choose Your Plan"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto",children:"Professional subtitle extraction with flexible pricing for every need"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12",children:Object.entries(w.D).map(([e,t],s)=>(0,i.jsx)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*s},viewport:{once:!0},className:"relative",children:(0,i.jsxs)(b.Zp,{className:`relative overflow-hidden ${t.popular?"border-purple-500 shadow-lg shadow-purple-500/20 scale-105":"border-slate-700"} bg-slate-800/80 backdrop-blur-sm h-full`,children:[t.popular&&(0,i.jsx)("div",{className:"absolute top-0 left-0 right-0",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium",children:"Most Popular"})}),(0,i.jsxs)(b.aR,{className:t.popular?"pt-12":"pt-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)(b.ZB,{className:"text-white flex items-center gap-2",children:["starter"===e&&(0,i.jsx)(n.A,{className:"w-5 h-5 text-blue-500"}),"pro"===e&&(0,i.jsx)(u.A,{className:"w-5 h-5 text-purple-500"}),"premium"===e&&(0,i.jsx)(p.A,{className:"w-5 h-5 text-yellow-500"}),t.name]})}),(0,i.jsx)(b.BT,{className:"text-gray-300",children:(0,i.jsxs)("div",{className:"flex items-baseline gap-1",children:[(0,i.jsxs)("span",{className:"text-3xl font-bold text-white",children:["$",t.price]}),(0,i.jsx)("span",{className:"text-gray-400",children:"/month"})]})})]}),(0,i.jsx)(b.Wu,{className:"space-y-4",children:(0,i.jsx)("ul",{className:"space-y-2",children:t.features.slice(0,4).map((e,t)=>(0,i.jsxs)("li",{className:"flex items-start gap-2 text-sm",children:[(0,i.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,i.jsx)("span",{className:"text-gray-300",children:e})]},t))})})]})},e))}),(0,i.jsx)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"text-center",children:(0,i.jsxs)(h.$,{onClick:t,variant:"outline",size:"lg",className:"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white",children:["View All Plans & Features",(0,i.jsx)(x.A,{className:"w-4 h-4 ml-2"})]})})]})}),(0,i.jsx)("section",{className:"py-12 sm:py-20 bg-gradient-to-r from-purple-800/20 to-pink-800/20",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 text-center",children:(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6",children:"Ready to Extract Subtitles?"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8 px-2",children:"Start extracting professional-quality subtitles in seconds"}),(0,i.jsxs)(h.$,{onClick:e,size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 sm:px-12 py-3 sm:py-4 text-lg sm:text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,i.jsx)(c.A,{className:"w-5 h-5 sm:w-6 sm:h-6 mr-2"}),"Start Extracting Now"]})]})})})]})};a()}catch(e){a(e)}})},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2439:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3213:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3220:e=>{e.exports=import("framer-motion")},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},5091:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},5154:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>x});var i=s(8732),l=s(3220),o=s(2237),r=s(8495),n=s(5091),c=s(3295),d=s(7290),m=s(3213),u=s(7139),p=e([l,o]);[l,o]=p.then?(await p)():p;let x=()=>{let e=[{step:1,title:"Paste YouTube URL",description:"Copy and paste any YouTube video URL into the input field. Our system will automatically validate the URL and detect if it's a valid YouTube video.",icon:(0,i.jsx)(r.A,{className:"w-8 h-8"}),image:"/images/step1-url-input.png",tips:["Works with any YouTube video URL format","Automatic URL validation with instant feedback","Supports both youtube.com and youtu.be links"]},{step:2,title:"Select Language",description:"Browse through available subtitle languages with our searchable interface. English is automatically selected when available.",icon:(0,i.jsx)(n.A,{className:"w-8 h-8"}),image:"/images/step2-language-select.png",tips:["Search through 70+ supported languages","Auto-generated subtitles clearly marked","English automatically selected as default"]},{step:3,title:"Extract & Process",description:"Watch the real-time progress as we extract and clean the subtitles. Our advanced processing ensures perfect formatting.",icon:(0,i.jsx)(c.A,{className:"w-8 h-8"}),image:"/images/step3-extraction.png",tips:["Real-time progress tracking","Advanced subtitle cleaning algorithms","Automatic formatting optimization"]},{step:4,title:"Preview & Download",description:"Preview your subtitles in VTT, SRT, or TXT format before downloading. See exactly what you'll get with full content preview.",icon:(0,i.jsx)(d.A,{className:"w-8 h-8"}),image:"/images/step4-preview-download.png",tips:["Preview in multiple formats","Full content scrollable preview","One-click download in preferred format"]}];return(0,i.jsx)("section",{id:"how-to-use",className:"py-12 sm:py-20 bg-slate-800/30",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"How to Use"}),(0,i.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Extract YouTube subtitles in 4 simple steps. No registration required, completely free to use."})]}),(0,i.jsx)("div",{className:"space-y-12 sm:space-y-16",children:e.map((t,s)=>(0,i.jsxs)(l.motion.div,{initial:{opacity:0,y:40},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*s},viewport:{once:!0},className:`flex flex-col ${s%2==0?"lg:flex-row":"lg:flex-row-reverse"} items-center gap-8 sm:gap-12`,children:[(0,i.jsxs)("div",{className:"flex-1 space-y-4 sm:space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full",children:(0,i.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-white",children:t.step})}),(0,i.jsx)("div",{className:"text-purple-400",children:t.icon})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-white mb-3 sm:mb-4",children:t.title}),(0,i.jsx)("p",{className:"text-base sm:text-lg text-gray-300 leading-relaxed",children:t.description})]}),(0,i.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,i.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white",children:"Key Features:"}),(0,i.jsx)("ul",{className:"space-y-2",children:t.tips.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-start space-x-2 sm:space-x-3",children:[(0,i.jsx)(m.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0 mt-0.5"}),(0,i.jsx)("span",{className:"text-sm sm:text-base text-gray-300",children:e})]},t))})]}),s<e.length-1&&(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-purple-400",children:[(0,i.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Next Step"}),(0,i.jsx)(u.A,{className:"w-3 h-3 sm:w-4 sm:h-4"})]})]}),(0,i.jsx)("div",{className:"flex-1 w-full",children:(0,i.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:(0,i.jsx)(o.Wu,{className:"p-0",children:(0,i.jsx)("div",{className:"aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center p-4",children:[(0,i.jsx)("div",{className:"w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-3 sm:mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center",children:t.icon}),(0,i.jsxs)("h4",{className:"text-white font-semibold mb-2 text-sm sm:text-base",children:["Step ",t.step," Preview"]}),(0,i.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Screenshot coming soon"})]})})})})})]},t.step))})]})})};a()}catch(e){a(e)}})},5594:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5619:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>b,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>x,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>N,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>y});var i=s(3885),l=s(237),o=s(1413),r=s(9616),n=s.n(r),c=s(2386),d=s(1370),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,o.M)(d,"default"),p=(0,o.M)(d,"getStaticProps"),x=(0,o.M)(d,"getStaticPaths"),h=(0,o.M)(d,"getServerSideProps"),b=(0,o.M)(d,"config"),g=(0,o.M)(d,"reportWebVitals"),y=(0,o.M)(d,"unstable_getStaticProps"),w=(0,o.M)(d,"unstable_getStaticPaths"),v=(0,o.M)(d,"unstable_getStaticParams"),f=(0,o.M)(d,"unstable_getServerProps"),j=(0,o.M)(d,"unstable_getServerSideProps"),N=new i.PagesRouteModule({definition:{kind:l.A.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},components:{App:c.default,Document:n()},userland:d});a()}catch(e){a(e)}})},5653:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5972:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.d(t,{A:()=>y});var i=s(8732),l=s(2015),o=s(3220),r=s(2237),n=s(7459),c=s(3295),d=s(6708),m=s(6596),u=s(703),p=s(3441),x=s(5974),h=s(5594),b=s(5653),g=e([o,r,n]);[o,r,n]=g.then?(await g)():g;let y=({showHeader:e=!0})=>{let[t,s]=(0,l.useState)([0]),a=e=>{s(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},g=[{question:"Can I download subtitles from any YouTube video?",answer:"Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),keywords:"download YouTube subtitles, YouTube video subtitles, extract YouTube captions"},{question:"Are auto-generated YouTube subtitles supported?",answer:"Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),keywords:"auto-generated YouTube captions, YouTube AI subtitles, automatic captions download"},{question:"Is it legal to download subtitles from YouTube videos?",answer:"For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.",icon:(0,i.jsx)(m.A,{className:"w-5 h-5"}),keywords:"legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles"},{question:"What subtitle formats can I download?",answer:"Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.",icon:(0,i.jsx)(u.A,{className:"w-5 h-5"}),keywords:"VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download"},{question:"How accurate are YouTube auto-generated subtitles?",answer:"YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.",icon:(0,i.jsx)(p.A,{className:"w-5 h-5"}),keywords:"YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability"},{question:"Can I download subtitles in different languages?",answer:"Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.",icon:(0,i.jsx)(x.A,{className:"w-5 h-5"}),keywords:"multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts"},{question:"Do I need to create an account to download subtitles?",answer:"No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.",icon:(0,i.jsx)(m.A,{className:"w-5 h-5"}),keywords:"free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor"},{question:"Can I download subtitles from YouTube playlists?",answer:"Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),keywords:"YouTube video subtitles, individual video captions, single video transcript download"},{question:"How fast is the subtitle extraction process?",answer:"Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),keywords:"fast YouTube subtitle download, quick caption extraction, instant YouTube transcript"},{question:"What should I do if subtitles aren't available for a video?",answer:"If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.",icon:(0,i.jsx)(p.A,{className:"w-5 h-5"}),keywords:"YouTube video no subtitles, missing YouTube captions, subtitle availability check"}];return(0,i.jsx)("div",{className:e?"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900":"",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e&&(0,i.jsxs)(o.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Frequently Asked Questions"}),(0,i.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Everything you need to know about downloading YouTube subtitles and captions"})]}),(0,i.jsx)("div",{className:"space-y-4",children:g.map((s,l)=>(0,i.jsx)(o.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:e?.1*(l+1):.05*l},children:(0,i.jsxs)(r.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:[(0,i.jsx)(n.$,{variant:"ghost",onClick:()=>a(l),className:"w-full p-6 text-left hover:bg-slate-700/50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"text-purple-400",children:s.icon}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-white",children:s.question})]}),(0,i.jsx)("div",{className:"text-purple-400",children:t.includes(l)?(0,i.jsx)(h.A,{className:"w-5 h-5"}):(0,i.jsx)(b.A,{className:"w-5 h-5"})})]})}),(0,i.jsx)(o.AnimatePresence,{children:t.includes(l)&&(0,i.jsx)(o.motion.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:(0,i.jsx)(r.Wu,{className:"px-6 pb-6 pt-0",children:(0,i.jsx)("div",{className:"pl-9",children:(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:s.answer})})})})})]})},l))}),e&&(0,i.jsx)(o.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,i.jsx)(r.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,i.jsxs)(r.Wu,{className:"p-8",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Still have questions?"}),(0,i.jsx)("p",{className:"text-gray-300 mb-6",children:"Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader."}),(0,i.jsx)(n.$,{onClick:()=>window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Contact Support"})]})})})]})})};a()}catch(e){a(e)}})},5974:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5979:e=>{e.exports=import("tailwind-merge")},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},6708:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},7139:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7290:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7637:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7910:e=>{e.exports=require("stream")},8495:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(5).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[616,441,975,312],()=>s(5619));module.exports=a})();