"use strict";(()=>{var e={};e.id=357,e.ids=[220,357],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},699:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{config:()=>b,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>y,routeModule:()=>N,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>g});var i=a(3885),r=a(237),o=a(1413),n=a(9616),l=a.n(n),c=a(2386),d=a(4793),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,o.M)(d,"default"),p=(0,o.M)(d,"getStaticProps"),h=(0,o.M)(d,"getStaticPaths"),x=(0,o.M)(d,"getServerSideProps"),b=(0,o.M)(d,"config"),y=(0,o.M)(d,"reportWebVitals"),g=(0,o.M)(d,"unstable_getStaticProps"),v=(0,o.M)(d,"unstable_getStaticPaths"),f=(0,o.M)(d,"unstable_getStaticParams"),w=(0,o.M)(d,"unstable_getServerProps"),j=(0,o.M)(d,"unstable_getServerSideProps"),N=new i.PagesRouteModule({definition:{kind:r.A.PAGES,page:"/disclaimer",pathname:"/disclaimer",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});s()}catch(e){s(e)}})},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},1680:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3220:e=>{e.exports=import("framer-motion")},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},4498:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4793:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>u});var i=a(8732),r=a(9788),o=a.n(r),n=a(4233),l=a(8405),c=a(5027),d=a(5547),m=e([l,c,d]);function u(){let e=(0,n.useRouter)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(o(),{children:[(0,i.jsx)("title",{children:"Disclaimer - DownloadYTSubtitles"}),(0,i.jsx)("meta",{name:"description",content:"Disclaimer for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,i.jsx)(l.A,{currentView:"disclaimer",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,i.jsx)(d.A,{onBack:()=>{e.push("/")}}),(0,i.jsx)(c.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[l,c,d]=m.then?(await m)():m,s()}catch(e){s(e)}})},5547:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{A:()=>h});var i=a(8732),r=a(3220),o=a(2237),n=a(7459),l=a(6383),c=a(6596),d=a(703),m=a(4498),u=a(1680),p=e([r,o,n]);[r,o,n]=p.then?(await p)():p;let h=({onBack:e})=>{let t=[{title:"Service Purpose",icon:(0,i.jsx)(l.A,{className:"w-5 h-5"}),content:["DownloadYTSubtitles.com is a free tool designed to extract publicly available subtitle data from YouTube videos.","We provide this service to help users access subtitle content for educational, research, and accessibility purposes.","Our tool only processes subtitle files that are already publicly accessible through YouTube's platform."]},{title:"Content Ownership",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),content:["All subtitle content extracted through our service belongs to the original video creators and YouTube.","We do not claim ownership of any extracted subtitle content or the underlying video material.","Users are responsible for respecting copyright laws and intellectual property rights when using extracted content.","Always credit the original content creators when using their subtitle content."]},{title:"Acceptable Use",icon:(0,i.jsx)(d.A,{className:"w-5 h-5"}),content:["This service is intended for personal, educational, and non-commercial use only.","Commercial use of extracted subtitles may require permission from the original content creators.","Users must comply with YouTube's Terms of Service and applicable copyright laws.","Fair use principles should guide your use of extracted subtitle content."]},{title:"Service Limitations",icon:(0,i.jsx)(m.A,{className:"w-5 h-5"}),content:["We cannot guarantee the availability or accuracy of subtitle content for all videos.","Subtitle availability depends on whether the video creator has enabled subtitles or YouTube has generated them.","Auto-generated subtitles may contain errors and should be reviewed before use.","Service availability may be interrupted for maintenance or technical reasons."]},{title:"Legal Compliance",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),content:["Users are responsible for ensuring their use of extracted content complies with local laws.","Different countries may have varying copyright and fair use regulations.","Educational and research use is generally more permissible than commercial use.","When in doubt, seek permission from the original content creator."]},{title:"No Warranty",icon:(0,i.jsx)(m.A,{className:"w-5 h-5"}),content:["This service is provided 'as is' without any warranties or guarantees.","We do not warrant the accuracy, completeness, or quality of extracted subtitles.","Users assume all risks associated with the use of this service.","We are not liable for any damages resulting from the use of extracted content."]}];return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)(n.$,{variant:"ghost",onClick:e,className:"text-white hover:text-purple-400",children:[(0,i.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,i.jsx)("div",{className:"text-right",children:(0,i.jsxs)("p",{className:"text-gray-400 text-sm",children:["Last updated: ",new Date().toLocaleDateString()]})})]}),(0,i.jsxs)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center mb-12",children:[(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Disclaimer"}),(0,i.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Important information about using DownloadYTSubtitles.com responsibly and legally"})]}),(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:(0,i.jsx)(o.Zp,{className:"bg-orange-900/20 border-orange-500/30",children:(0,i.jsx)(o.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)(m.A,{className:"w-8 h-8 text-orange-400 flex-shrink-0 mt-1"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-orange-400 mb-2",children:"Important Notice"}),(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("strong",{children:"Use Responsibly:"})," This tool extracts publicly available subtitle data. Users are responsible for ensuring their use complies with copyright laws and YouTube's Terms of Service. Always respect content creators' rights and give proper attribution."]})]})]})})})}),(0,i.jsx)("div",{className:"space-y-6",children:t.map((e,t)=>(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(t+3)},children:(0,i.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,i.jsx)(o.aR,{children:(0,i.jsxs)(o.ZB,{className:"text-white flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,i.jsx)("span",{children:e.title})]})}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:e.content.map((e,t)=>(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e},t))})})]})},t))}),(0,i.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,i.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,i.jsxs)(o.Wu,{className:"p-6",children:[(0,i.jsx)("p",{className:"text-gray-300 mb-4",children:"By using DownloadYTSubtitles.com, you acknowledge that you have read, understood, and agree to comply with this disclaimer and use the service responsibly."}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["This disclaimer is effective as of ",new Date().toLocaleDateString()," and may be updated from time to time to reflect changes in our service or legal requirements."]})]})})})]})})};s()}catch(e){s(e)}})},5979:e=>{e.exports=import("tailwind-merge")},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},6383:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[616,441,975,312],()=>a(699));module.exports=s})();