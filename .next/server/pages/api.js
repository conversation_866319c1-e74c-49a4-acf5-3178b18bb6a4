"use strict";(()=>{var e={};e.id=880,e.ids=[880],e.modules={237:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},1413:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5884:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>l,routeModule:()=>P});var n={};r.r(n),r.d(n,{default:()=>u});var o=r(7602),i=r(237),s=r(1413),a=r(9021),d=r(3873);let u=(e,t)=>{if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return void t.status(200).end();try{let e=(0,d.join)(process.cwd(),"dist","client","index.html"),r=(0,a.readFileSync)(e,"utf8");t.setHeader("Content-Type","text/html"),t.status(200).send(r)}catch(e){console.error("Error serving index.html:",e),t.status(500).json({error:"Failed to serve application"})}},l=(0,s.M)(n,"default"),c=(0,s.M)(n,"config"),P=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api",pathname:"/api",bundlePath:"",filename:""},userland:n})},7602:(e,t,r)=>{e.exports=r(5600)},9021:e=>{e.exports=require("fs")}};var t=require("../webpack-runtime.js");t.C(e);var r=t(t.s=5884);module.exports=r})();