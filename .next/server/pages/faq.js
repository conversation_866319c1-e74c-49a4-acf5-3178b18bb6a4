"use strict";(()=>{var e={};e.id=816,e.ids=[220,816],e.modules={206:e=>{e.exports=require("@stripe/stripe-js")},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},802:e=>{e.exports=import("clsx")},824:e=>{e.exports=require("@radix-ui/react-icons")},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},2549:e=>{e.exports=import("@radix-ui/react-avatar")},2893:e=>{e.exports=import("react-hot-toast")},3220:e=>{e.exports=import("framer-motion")},3873:e=>{e.exports=require("path")},3939:e=>{e.exports=require("@supabase/supabase-js")},4075:e=>{e.exports=require("zlib")},5501:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{config:()=>x,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>m,getStaticProps:()=>b,reportWebVitals:()=>g,routeModule:()=>j,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>T,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>w});var o=a(3885),i=a(237),r=a(1413),n=a(9616),l=a.n(n),u=a(2386),d=a(9638),c=e([u,d]);[u,d]=c.then?(await c)():c;let p=(0,r.M)(d,"default"),b=(0,r.M)(d,"getStaticProps"),m=(0,r.M)(d,"getStaticPaths"),h=(0,r.M)(d,"getServerSideProps"),x=(0,r.M)(d,"config"),g=(0,r.M)(d,"reportWebVitals"),w=(0,r.M)(d,"unstable_getStaticProps"),y=(0,r.M)(d,"unstable_getStaticPaths"),v=(0,r.M)(d,"unstable_getStaticParams"),f=(0,r.M)(d,"unstable_getServerProps"),T=(0,r.M)(d,"unstable_getServerSideProps"),j=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/faq",pathname:"/faq",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:d});s()}catch(e){s(e)}})},5594:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5653:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5972:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{A:()=>w});var o=a(8732),i=a(2015),r=a(3220),n=a(2237),l=a(7459),u=a(3295),d=a(6708),c=a(6596),p=a(703),b=a(3441),m=a(5974),h=a(5594),x=a(5653),g=e([r,n,l]);[r,n,l]=g.then?(await g)():g;let w=({showHeader:e=!0})=>{let[t,a]=(0,i.useState)([0]),s=e=>{a(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},g=[{question:"Can I download subtitles from any YouTube video?",answer:"Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.",icon:(0,o.jsx)(u.A,{className:"w-5 h-5"}),keywords:"download YouTube subtitles, YouTube video subtitles, extract YouTube captions"},{question:"Are auto-generated YouTube subtitles supported?",answer:"Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.",icon:(0,o.jsx)(d.A,{className:"w-5 h-5"}),keywords:"auto-generated YouTube captions, YouTube AI subtitles, automatic captions download"},{question:"Is it legal to download subtitles from YouTube videos?",answer:"For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.",icon:(0,o.jsx)(c.A,{className:"w-5 h-5"}),keywords:"legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles"},{question:"What subtitle formats can I download?",answer:"Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.",icon:(0,o.jsx)(p.A,{className:"w-5 h-5"}),keywords:"VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download"},{question:"How accurate are YouTube auto-generated subtitles?",answer:"YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.",icon:(0,o.jsx)(b.A,{className:"w-5 h-5"}),keywords:"YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability"},{question:"Can I download subtitles in different languages?",answer:"Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.",icon:(0,o.jsx)(m.A,{className:"w-5 h-5"}),keywords:"multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts"},{question:"Do I need to create an account to download subtitles?",answer:"No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.",icon:(0,o.jsx)(c.A,{className:"w-5 h-5"}),keywords:"free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor"},{question:"Can I download subtitles from YouTube playlists?",answer:"Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.",icon:(0,o.jsx)(u.A,{className:"w-5 h-5"}),keywords:"YouTube video subtitles, individual video captions, single video transcript download"},{question:"How fast is the subtitle extraction process?",answer:"Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.",icon:(0,o.jsx)(d.A,{className:"w-5 h-5"}),keywords:"fast YouTube subtitle download, quick caption extraction, instant YouTube transcript"},{question:"What should I do if subtitles aren't available for a video?",answer:"If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.",icon:(0,o.jsx)(b.A,{className:"w-5 h-5"}),keywords:"YouTube video no subtitles, missing YouTube captions, subtitle availability check"}];return(0,o.jsx)("div",{className:e?"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900":"",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e&&(0,o.jsxs)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,o.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Frequently Asked Questions"}),(0,o.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Everything you need to know about downloading YouTube subtitles and captions"})]}),(0,o.jsx)("div",{className:"space-y-4",children:g.map((a,i)=>(0,o.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:e?.1*(i+1):.05*i},children:(0,o.jsxs)(n.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:[(0,o.jsx)(l.$,{variant:"ghost",onClick:()=>s(i),className:"w-full p-6 text-left hover:bg-slate-700/50 transition-colors",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:"text-purple-400",children:a.icon}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-white",children:a.question})]}),(0,o.jsx)("div",{className:"text-purple-400",children:t.includes(i)?(0,o.jsx)(h.A,{className:"w-5 h-5"}):(0,o.jsx)(x.A,{className:"w-5 h-5"})})]})}),(0,o.jsx)(r.AnimatePresence,{children:t.includes(i)&&(0,o.jsx)(r.motion.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:(0,o.jsx)(n.Wu,{className:"px-6 pb-6 pt-0",children:(0,o.jsx)("div",{className:"pl-9",children:(0,o.jsx)("p",{className:"text-gray-300 leading-relaxed",children:a.answer})})})})})]})},i))}),e&&(0,o.jsx)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,o.jsx)(n.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,o.jsxs)(n.Wu,{className:"p-8",children:[(0,o.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Still have questions?"}),(0,o.jsx)("p",{className:"text-gray-300 mb-6",children:"Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader."}),(0,o.jsx)(l.$,{onClick:()=>window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Contact Support"})]})})})]})})};s()}catch(e){s(e)}})},5974:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5979:e=>{e.exports=import("tailwind-merge")},6307:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},6708:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(5).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},8938:e=>{e.exports=import("class-variance-authority")},9021:e=>{e.exports=require("fs")},9638:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>p});var o=a(8732),i=a(9788),r=a.n(i),n=a(4233),l=a(8405),u=a(5027),d=a(5972),c=e([l,u,d]);function p(){let e=(0,n.useRouter)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r(),{children:[(0,o.jsx)("title",{children:"FAQ - DownloadYTSubtitles"}),(0,o.jsx)("meta",{name:"description",content:"Frequently Asked Questions about DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,o.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,o.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,o.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,o.jsx)(l.A,{currentView:"faq",onNavigate:t=>{e.push(`/${t}`)},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,o.jsx)(d.A,{showHeader:!0}),(0,o.jsx)(u.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}[l,u,d]=c.then?(await c)():c,s()}catch(e){s(e)}})},9640:e=>{e.exports=import("@radix-ui/react-slot")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[616,441,975,312],()=>a(5501));module.exports=s})();