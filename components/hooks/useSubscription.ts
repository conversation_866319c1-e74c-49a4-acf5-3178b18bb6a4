import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { PRICING_TIERS, type PricingTier } from '@/lib/stripe';
import type { Subscription, UsageStats } from '@/types/subscription';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

export const useSubscription = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [usage, setUsage] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
    } else {
      setSubscription(null);
      setUsage(null);
      setLoading(false);
    }
  }, [user]);

  const fetchSubscriptionData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch subscription
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        throw subscriptionError;
      }

      // Fetch usage stats
      const { data: usageData, error: usageError } = await supabase
        .from('usage_stats')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (usageError && usageError.code !== 'PGRST116') {
        throw usageError;
      }

      setSubscription(subscriptionData);
      setUsage(usageData);
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to fetch subscription data');
    } finally {
      setLoading(false);
    }
  };

  const canExtractVideo = (videoLengthMinutes: number = 0): boolean => {
    if (!subscription || subscription.status !== 'active') {
      return false;
    }

    const tier = PRICING_TIERS[subscription.tier as PricingTier];
    if (!tier) return false;

    // Check video length limit
    if (tier.limits.maxVideoLength !== -1 && videoLengthMinutes > tier.limits.maxVideoLength) {
      return false;
    }

    // Check monthly extraction limit
    if (tier.limits.videosPerMonth === -1) {
      return true; // Unlimited
    }

    const currentUsage = usage?.videos_extracted_this_month || 0;
    return currentUsage < tier.limits.videosPerMonth;
  };

  const getRemainingExtractions = (): number => {
    if (!subscription || subscription.status !== 'active') {
      return 0;
    }

    const tier = PRICING_TIERS[subscription.tier as PricingTier];
    if (!tier) return 0;

    if (tier.limits.videosPerMonth === -1) {
      return -1; // Unlimited
    }

    const currentUsage = usage?.videos_extracted_this_month || 0;
    return Math.max(0, tier.limits.videosPerMonth - currentUsage);
  };

  const createCheckoutSession = async (tier: PricingTier): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated');
    }

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: PRICING_TIERS[tier].priceId,
          userId: user.id,
          userEmail: user.email
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { sessionId } = await response.json();
      return sessionId;
    } catch (err) {
      console.error('Error creating checkout session:', err);
      toast.error('Failed to create checkout session');
      throw err;
    }
  };

  const cancelSubscription = async (): Promise<void> => {
    if (!subscription) {
      throw new Error('No active subscription found');
    }

    try {
      const response = await fetch('/api/user/subscription', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.stripe_subscription_id
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      toast.success('Subscription cancelled successfully');
      await fetchSubscriptionData();
    } catch (err) {
      console.error('Error cancelling subscription:', err);
      toast.error('Failed to cancel subscription');
      throw err;
    }
  };

  const refreshSubscription = async (): Promise<void> => {
    await fetchSubscriptionData();
  };

  return {
    subscription,
    usage,
    loading,
    error,
    canExtractVideo,
    getRemainingExtractions,
    createCheckoutSession,
    cancelSubscription,
    refreshSubscription
  };
};
