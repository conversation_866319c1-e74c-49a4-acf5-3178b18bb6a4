import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/components/contexts/AuthContext';
import { useSubscription } from '@/components/hooks/useSubscription';
import SubscriptionStatus from '@/components/subscription/SubscriptionStatus';
import { ArrowLeft, User, CreditCard, Settings, Download } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserDashboardProps {
  onBack?: () => void;
  onNavigate?: (view: string) => void;
}

const UserDashboard: React.FC<UserDashboardProps> = ({ onBack, onNavigate }) => {
  const { user } = useAuth();
  const { subscription, cancelSubscription } = useSubscription();
  const [cancelling, setCancelling] = React.useState(false);

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700 p-8">
          <CardContent className="text-center">
            <h2 className="text-xl font-semibold text-white mb-4">Access Denied</h2>
            <p className="text-gray-300 mb-4">Please sign in to access your dashboard.</p>
            <Button onClick={onBack} variant="outline">
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const displayName = user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User';
  const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture;

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;
    
    const confirmed = window.confirm(
      'Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period.'
    );
    
    if (confirmed) {
      try {
        setCancelling(true);
        await cancelSubscription();
      } catch (error) {
        console.error('Error cancelling subscription:', error);
      } finally {
        setCancelling(false);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="max-w-6xl mx-auto px-3 sm:px-6 lg:px-8 py-12">
        {/* Back Button */}
        {onBack && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="mb-8"
          >
            <Button
              onClick={onBack}
              variant="ghost"
              className="text-gray-300 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </motion.div>
        )}

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <div className="flex items-center gap-4 mb-6">
            <Avatar className="h-16 w-16">
              <AvatarImage src={avatarUrl} alt={displayName} />
              <AvatarFallback className="bg-purple-600 text-white text-lg">
                {getInitials(displayName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold text-white">{displayName}</h1>
              <p className="text-gray-300">{user.email}</p>
            </div>
          </div>
        </motion.div>

        {/* Dashboard Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 bg-slate-800/80 backdrop-blur-sm border border-slate-700">
              <TabsTrigger value="overview" className="data-[state=active]:bg-purple-600">
                <User className="w-4 h-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="subscription" className="data-[state=active]:bg-purple-600">
                <CreditCard className="w-4 h-4 mr-2" />
                Subscription
              </TabsTrigger>
              <TabsTrigger value="settings" className="data-[state=active]:bg-purple-600">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <SubscriptionStatus />
                
                <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Download className="w-5 h-5" />
                      Quick Actions
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Common tasks and shortcuts
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      onClick={() => onNavigate?.('extractor')}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      Extract Subtitles
                    </Button>
                    <Button 
                      onClick={() => onNavigate?.('pricing')}
                      variant="outline"
                      className="w-full border-slate-600 text-gray-300 hover:text-white"
                    >
                      View Plans
                    </Button>
                    <Button 
                      onClick={() => onNavigate?.('faq')}
                      variant="ghost"
                      className="w-full text-gray-300 hover:text-white"
                    >
                      Help & FAQ
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="subscription" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <SubscriptionStatus />
                </div>
                
                <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white">Manage Subscription</CardTitle>
                    <CardDescription className="text-gray-300">
                      Update your plan or billing information
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      onClick={() => onNavigate?.('pricing')}
                      className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                    >
                      Change Plan
                    </Button>
                    
                    {subscription && subscription.status === 'active' && !subscription.cancel_at_period_end && (
                      <Button 
                        onClick={handleCancelSubscription}
                        disabled={cancelling}
                        variant="destructive"
                        className="w-full"
                      >
                        {cancelling ? 'Cancelling...' : 'Cancel Subscription'}
                      </Button>
                    )}
                    
                    {subscription?.cancel_at_period_end && (
                      <div className="text-sm text-yellow-400 p-3 bg-yellow-400/10 rounded-lg border border-yellow-400/20">
                        Your subscription will cancel at the end of the current billing period.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Account Settings</CardTitle>
                  <CardDescription className="text-gray-300">
                    Manage your account preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Email</label>
                    <div className="text-white bg-slate-700 p-3 rounded-lg">
                      {user.email}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Full Name</label>
                    <div className="text-white bg-slate-700 p-3 rounded-lg">
                      {displayName}
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-slate-700">
                    <p className="text-sm text-gray-400">
                      Account settings are managed through your Google account. 
                      Changes made there will be reflected here automatically.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
};

export default UserDashboard;
