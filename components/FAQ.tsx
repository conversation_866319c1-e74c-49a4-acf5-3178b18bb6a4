import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, HelpCircle, Download, Globe, Shield, FileText, Zap } from 'lucide-react';

interface FAQProps {
  showHeader?: boolean;
}

const FAQ = ({ showHeader = true }: FAQProps) => {
  const [openItems, setOpenItems] = useState<number[]>([0]); // First item open by default

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqs = [
    {
      question: "Can I download subtitles from any YouTube video?",
      answer: "Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.",
      icon: <Download className="w-5 h-5" />,
      keywords: "download YouTube subtitles, YouTube video subtitles, extract YouTube captions"
    },
    {
      question: "Are auto-generated YouTube subtitles supported?",
      answer: "Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.",
      icon: <Zap className="w-5 h-5" />,
      keywords: "auto-generated YouTube captions, YouTube AI subtitles, automatic captions download"
    },
    {
      question: "Is it legal to download subtitles from YouTube videos?",
      answer: "For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.",
      icon: <Shield className="w-5 h-5" />,
      keywords: "legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles"
    },
    {
      question: "What subtitle formats can I download?",
      answer: "Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.",
      icon: <FileText className="w-5 h-5" />,
      keywords: "VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download"
    },
    {
      question: "How accurate are YouTube auto-generated subtitles?",
      answer: "YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.",
      icon: <HelpCircle className="w-5 h-5" />,
      keywords: "YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability"
    },
    {
      question: "Can I download subtitles in different languages?",
      answer: "Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.",
      icon: <Globe className="w-5 h-5" />,
      keywords: "multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts"
    },
    {
      question: "Do I need to create an account to download subtitles?",
      answer: "No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.",
      icon: <Shield className="w-5 h-5" />,
      keywords: "free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor"
    },
    {
      question: "Can I download subtitles from YouTube playlists?",
      answer: "Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.",
      icon: <Download className="w-5 h-5" />,
      keywords: "YouTube video subtitles, individual video captions, single video transcript download"
    },
    {
      question: "How fast is the subtitle extraction process?",
      answer: "Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.",
      icon: <Zap className="w-5 h-5" />,
      keywords: "fast YouTube subtitle download, quick caption extraction, instant YouTube transcript"
    },
    {
      question: "What should I do if subtitles aren't available for a video?",
      answer: "If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.",
      icon: <HelpCircle className="w-5 h-5" />,
      keywords: "YouTube video no subtitles, missing YouTube captions, subtitle availability check"
    }
  ];

  return (
    <div className={showHeader ? "min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900" : ""}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {showHeader && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Everything you need to know about downloading YouTube subtitles and captions
            </p>
          </motion.div>
        )}

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: showHeader ? 0.1 * (index + 1) : 0.05 * index }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden">
                <Button
                  variant="ghost"
                  onClick={() => toggleItem(index)}
                  className="w-full p-6 text-left hover:bg-slate-700/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-purple-400">
                        {faq.icon}
                      </div>
                      <h3 className="text-lg font-semibold text-white">
                        {faq.question}
                      </h3>
                    </div>
                    <div className="text-purple-400">
                      {openItems.includes(index) ? (
                        <ChevronUp className="w-5 h-5" />
                      ) : (
                        <ChevronDown className="w-5 h-5" />
                      )}
                    </div>
                  </div>
                </Button>
                
                <AnimatePresence>
                  {openItems.includes(index) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <CardContent className="px-6 pb-6 pt-0">
                        <div className="pl-9">
                          <p className="text-gray-300 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      </CardContent>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </motion.div>
          ))}
        </div>

        {showHeader && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-12 text-center"
          >
            <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-white mb-4">
                  Still have questions?
                </h3>
                <p className="text-gray-300 mb-6">
                  Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader.
                </p>
                <Button
                  onClick={() => window.open('https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog', '_blank')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default FAQ;
