import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Header from '../components/Header';
import Footer from '../components/Footer';
import LandingPage from '../components/LandingPage';

export default function Home() {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/extractor');
  };

  const handleNavigate = (view: string) => {
    router.push(`/${view}`);
  };

  const handleFeedback = () => {
    window.open('https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog', '_blank');
  };

  const handleTermsClick = () => {
    router.push('/terms');
  };

  const handlePrivacyClick = () => {
    router.push('/privacy');
  };

  const handleDisclaimerClick = () => {
    router.push('/disclaimer');
  };

  return (
    <>
      <Head>
        <title>Download YouTube Subtitles - Free YT Caption Extractor - DownloadYTSubtitles</title>
        <meta name="description" content="Free YouTube subtitle downloader and transcript extractor. Download YT captions in VTT and TXT formats from auto-generated and manual subtitles." />
        <meta name="keywords" content="YouTube subtitles, download YouTube captions, YouTube transcript extractor, YT subtitles, video captions" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen font-sans">
        <Header
          currentView="landing"
          onNavigate={handleNavigate}
          onFeedback={handleFeedback}
        />
        
        <LandingPage
          onGetStarted={handleGetStarted}
          onNavigateToPricing={() => handleNavigate('pricing')}
        />
        
        <Footer
          onTermsClick={handleTermsClick}
          onPrivacyClick={handlePrivacyClick}
          onDisclaimerClick={handleDisclaimerClick}
        />
      </div>
    </>
  );
}
