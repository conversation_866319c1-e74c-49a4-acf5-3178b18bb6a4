import ytdl from '@distube/ytdl-core';
import { SocksProxyAgent } from 'socks-proxy-agent';


// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

// SOCKS5 or HTTPS proxy URI format
const agent = ytdl.createProxyAgent(process.env.SOCKET_URL);
// const agent = new SocksProxyAgent(
// 	'socks5://*************:8443'
// );
export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;
    
    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get video info including available subtitles
    const videoInfo = await ytdl.getInfo(`https://www.youtube.com/watch?v=${videoId}`, {agent});

    const availableLanguages = [];

    // Get available subtitle tracks
    if (videoInfo.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = videoInfo.player_response.captions.playerCaptionsTracklistRenderer.captionTracks;

      captionTracks.forEach(track => {
        const langCode = track.languageCode;
        const langName = track.name?.simpleText || getLanguageName(langCode);
        const isAutoGenerated = track.kind === 'asr';

        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: isAutoGenerated
        });
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    res.status(200).json({
      videoId,
      title: videoInfo.videoDetails?.title || 'YouTube Video',
      thumbnail: videoInfo.videoDetails?.thumbnails?.[0]?.url || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
