import { loadStripe } from '@stripe/stripe-js';

const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey) {
  throw new Error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
}

export const stripePromise = loadStripe(stripePublishableKey);

// Pricing configuration
export const PRICING_TIERS = {
  starter: {
    name: 'Starter',
    price: 9,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',
    features: [
      'Extract subtitles from 50 videos/month',
      'VTT and TXT format downloads',
      'Auto-generated caption support',
      'Basic language detection',
      'Email support'
    ],
    limits: {
      videosPerMonth: 50,
      maxVideoLength: 60 // minutes
    },
    popular: false
  },
  pro: {
    name: 'Pro',
    price: 19,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
    features: [
      'Extract subtitles from 200 videos/month',
      'All format downloads (VTT, TXT, SRT)',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'Priority support'
    ],
    limits: {
      videosPerMonth: 200,
      maxVideoLength: 180 // minutes
    },
    popular: true
  },
  premium: {
    name: 'Premium',
    price: 39,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID || 'price_premium_monthly',
    features: [
      'Unlimited video extractions',
      'All format downloads',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'API access',
      'Custom integrations',
      'Priority support'
    ],
    limits: {
      videosPerMonth: -1, // unlimited
      maxVideoLength: -1 // unlimited
    },
    popular: false
  }
} as const;

export type PricingTier = keyof typeof PRICING_TIERS;
